<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App Vapi Flow (Exact Replica)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test App Vapi Flow (Exact Replica)</h1>
        <p>This test replicates your app's exact Vapi loading and initialization process.</p>
        
        <div id="status" class="status info">
            Ready to test your app's exact flow...
        </div>

        <button id="testFlow" onclick="testCompleteFlow()">
            🚀 Test Complete App Flow
        </button>
        
        <button id="testMicrophone" onclick="testMicrophone()">
            🎤 Test Microphone
        </button>
        
        <button id="startCall" onclick="startTestCall()" disabled>
            📞 Start Test Call
        </button>
        
        <button id="stopCall" onclick="stopTestCall()" disabled>
            ⏹️ Stop Call
        </button>

        <div id="logs" class="log"></div>
    </div>

    <script type="module">
        // Configuration matching your app exactly
        const API_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
        const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
        
        let VapiClass = null;
        let vapi = null;
        let isCallActive = false;
        let loadingPromise = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function updateButtons() {
            document.getElementById('startCall').disabled = !vapi || isCallActive;
            document.getElementById('stopCall').disabled = !isCallActive;
        }
        
        // Replicate your app's exact Vapi loading logic
        async function loadVapiSDK() {
            // Return cached result if already loaded
            if (VapiClass) {
                log('[VapiLoader] Vapi SDK already loaded');
                return VapiClass;
            }

            // Return existing loading promise if already loading
            if (loadingPromise) {
                log('[VapiLoader] Vapi SDK loading in progress, waiting...');
                return loadingPromise;
            }

            // Start loading process (exactly like your app)
            loadingPromise = (async () => {
                log('[VapiLoader] Starting Vapi SDK loading process');

                try {
                    // Try to import the installed package (this will fail in browser, but we'll try)
                    log('[VapiLoader] Attempting to import @vapi-ai/web package');
                    try {
                        const VapiModule = await import('https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.js');
                        
                        // Extract the Vapi class from the module
                        VapiClass = VapiModule.default || VapiModule.Vapi || VapiModule;
                        
                        if (typeof VapiClass === 'function') {
                            log('[VapiLoader] ✅ Successfully loaded Vapi SDK from CDN import', 'success');
                            
                            // Expose globally for compatibility
                            window.Vapi = VapiClass;
                            window.__VAPI_BUNDLED__ = VapiClass;
                            
                            // Test instantiation
                            try {
                                const testInstance = new VapiClass('test-key');
                                if (testInstance && typeof testInstance.start === 'function') {
                                    log('[VapiLoader] ✅ Vapi SDK validation successful', 'success');
                                }
                            } catch (testError) {
                                log('[VapiLoader] ⚠️ Vapi SDK loaded but validation failed: ' + testError.message, 'warning');
                            }
                            
                            return VapiClass;
                        } else {
                            throw new Error('Imported module does not contain a valid Vapi constructor');
                        }
                    } catch (importError) {
                        log('[VapiLoader] ❌ Failed to import @vapi-ai/web package: ' + importError.message, 'error');
                        
                        // Fallback: try to use CDN-loaded version if available
                        if (window.Vapi && typeof window.Vapi === 'function') {
                            log('[VapiLoader] 🔄 Using CDN-loaded Vapi SDK as fallback');
                            VapiClass = window.Vapi;
                            return VapiClass;
                        }
                        
                        // Final fallback: load from CDN (exactly like your app)
                        log('[VapiLoader] 🔄 Attempting to load from CDN as final fallback');
                        try {
                            await loadFromCDN();
                            if (window.Vapi && typeof window.Vapi === 'function') {
                                VapiClass = window.Vapi;
                                log('[VapiLoader] ✅ Successfully loaded from CDN fallback', 'success');
                                return VapiClass;
                            }
                        } catch (cdnError) {
                            log('[VapiLoader] ❌ CDN fallback also failed: ' + cdnError.message, 'error');
                        }
                        
                        throw new Error('Failed to load Vapi SDK from all sources');
                    }
                } catch (error) {
                    log('[VapiLoader] ❌ Complete loading failure: ' + error.message, 'error');
                    throw error;
                }
            })();

            return loadingPromise;
        }
        
        // Load Vapi SDK from CDN as fallback (exactly like your app)
        async function loadFromCDN() {
            return new Promise((resolve, reject) => {
                // Don't load if already available
                if (window.Vapi) {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = 'https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js';
                script.async = true;
                script.crossOrigin = 'anonymous';

                const timeout = setTimeout(() => {
                    script.remove();
                    reject(new Error('CDN loading timeout'));
                }, 10000);

                script.onload = () => {
                    clearTimeout(timeout);
                    log('[VapiLoader] ✅ Successfully loaded from CDN');
                    resolve();
                };

                script.onerror = () => {
                    clearTimeout(timeout);
                    script.remove();
                    reject(new Error('Failed to load from CDN'));
                };

                document.head.appendChild(script);
            });
        }
        
        // Create Vapi instance (exactly like your app)
        async function createVapiInstance(apiKey) {
            if (!VapiClass) {
                log('[VapiLoader] Vapi not loaded, loading now...');
                await loadVapiSDK();
            }

            if (!VapiClass) {
                throw new Error('Failed to load Vapi SDK');
            }

            if (!apiKey || typeof apiKey !== 'string') {
                throw new Error('API key must be a non-empty string');
            }

            try {
                const vapi = new VapiClass(apiKey);
                log(`[VapiLoader] ✅ Vapi instance created with key: ${apiKey.substring(0, 8)}...`, 'success');
                return vapi;
            } catch (error) {
                log(`[VapiLoader] ❌ Failed to create Vapi instance: ${error.message}`, 'error');
                throw error;
            }
        }
        
        async function testMicrophone() {
            updateStatus('Testing microphone access...', 'info');
            log('🎤 Testing microphone access...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                log('✅ Microphone access granted', 'success');
                updateStatus('Microphone test passed', 'success');
                return true;
            } catch (error) {
                log(`❌ Microphone access denied: ${error.message}`, 'error');
                updateStatus('Microphone test failed - grant permissions', 'error');
                return false;
            }
        }
        
        async function initializeVapi() {
            updateStatus('Initializing Vapi SDK (using your app\'s exact method)...', 'info');
            log('🔧 Initializing Vapi SDK using your app\'s exact loading process...');
            
            try {
                // Use your app's exact loading process
                VapiClass = await loadVapiSDK();
                
                if (!VapiClass) {
                    throw new Error('Failed to load Vapi SDK from all sources');
                }
                
                log(`✅ Vapi SDK loaded successfully, creating instance with key: ${API_KEY.substring(0, 8)}...`);
                
                // Create Vapi instance using your app's exact method
                vapi = await createVapiInstance(API_KEY);
                
                // Set up event listeners (exactly like your app)
                vapi.on('call-start', () => {
                    log('📞 Call started!', 'success');
                    isCallActive = true;
                    updateButtons();
                    updateStatus('Call active', 'success');
                });
                
                vapi.on('call-end', () => {
                    log('📞 Call ended', 'info');
                    isCallActive = false;
                    updateButtons();
                    updateStatus('Call ended', 'info');
                });
                
                vapi.on('error', (error) => {
                    log(`❌ Vapi error: ${error.message}`, 'error');
                    updateStatus(`Error: ${error.message}`, 'error');
                });
                
                vapi.on('message', (message) => {
                    if (message.type === 'transcript') {
                        log(`💬 ${message.role}: ${message.transcript}`);
                    }
                });
                
                log('✅ Vapi instance created and event listeners set up (exactly like your app)', 'success');
                updateStatus('Vapi initialized successfully using your app\'s method', 'success');
                updateButtons();
                return true;
                
            } catch (error) {
                log(`❌ Failed to initialize Vapi: ${error.message}`, 'error');
                updateStatus(`Initialization failed: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function startTestCall() {
            if (!vapi) {
                log('❌ Vapi not initialized', 'error');
                return;
            }
            
            updateStatus('Starting call...', 'info');
            log(`📞 Starting call with assistant: ${ASSISTANT_ID}`);
            
            try {
                // This is exactly how your app starts calls
                await vapi.start(ASSISTANT_ID);
                log('✅ Call start request sent', 'success');
                
            } catch (error) {
                log(`❌ Failed to start call: ${error.message}`, 'error');
                updateStatus(`Call failed: ${error.message}`, 'error');
            }
        }
        
        async function stopTestCall() {
            if (!vapi || !isCallActive) {
                log('❌ No active call to stop', 'error');
                return;
            }
            
            updateStatus('Stopping call...', 'info');
            log('⏹️ Stopping call...');
            
            try {
                await vapi.stop();
                log('✅ Call stopped', 'success');
                
            } catch (error) {
                log(`❌ Failed to stop call: ${error.message}`, 'error');
                updateStatus(`Stop failed: ${error.message}`, 'error');
            }
        }
        
        async function testCompleteFlow() {
            updateStatus('Running complete flow test (your app\'s exact process)...', 'info');
            document.getElementById('logs').innerHTML = '';
            
            log('🚀 Starting complete Vapi flow test...');
            log('This replicates EXACTLY how your LegalScout Voice app works');
            
            // Step 1: Test microphone
            log('\n📋 Step 1: Testing microphone access');
            const microphoneOk = await testMicrophone();
            
            if (!microphoneOk) {
                log('❌ Cannot proceed without microphone access', 'error');
                updateStatus('Test failed - microphone required', 'error');
                return;
            }
            
            // Step 2: Initialize Vapi using your app's exact method
            log('\n📋 Step 2: Initializing Vapi SDK (using your app\'s exact loading process)');
            const vapiOk = await initializeVapi();
            
            if (!vapiOk) {
                log('❌ Cannot proceed without Vapi initialization', 'error');
                updateStatus('Test failed - Vapi initialization failed', 'error');
                return;
            }
            
            log('\n✅ Complete flow test passed!', 'success');
            log('💡 You can now test actual calls with the buttons above');
            log('🎯 This test uses your app\'s EXACT loading and initialization process');
            updateStatus('Flow test completed - ready for calls (using your app\'s exact method)', 'success');
        }
        
        // Make functions available globally
        window.testCompleteFlow = testCompleteFlow;
        window.testMicrophone = testMicrophone;
        window.startTestCall = startTestCall;
        window.stopTestCall = stopTestCall;
        
        // Auto-run flow test when page loads
        window.addEventListener('load', () => {
            setTimeout(testCompleteFlow, 1000);
        });
    </script>
</body>
</html>
