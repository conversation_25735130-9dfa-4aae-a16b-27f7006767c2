/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface ServerMessageResponseAssistantRequest {
    /**
     * This is the destination to transfer the inbound call to. This will immediately transfer without using any assistants.
     *
     * If this is sent, `assistantId`, `assistant`, `squadId`, and `squad` are ignored.
     */
    destination?: Vapi.ServerMessageResponseAssistantRequestDestination;
    /** This is the assistant that will be used for the call. To use a transient assistant, use `assistant` instead. */
    assistantId?: string;
    /**
     * This is the assistant that will be used for the call. To use an existing assistant, use `assistantId` instead.
     *
     * If you're unsure why you're getting an invalid assistant, try logging your response and send the JSON blob to POST /assistant which will return the validation errors.
     */
    assistant?: Vapi.CreateAssistantDto;
    /** These are the overrides for the `assistant` or `assistantId`'s settings and template variables. */
    assistantOverrides?: Vapi.AssistantOverrides;
    /** This is the squad that will be used for the call. To use a transient squad, use `squad` instead. */
    squadId?: string;
    /** This is a squad that will be used for the call. To use an existing squad, use `squadId` instead. */
    squad?: Vapi.CreateSquadDto;
    /**
     * This is the error if the call shouldn't be accepted. This is spoken to the customer.
     *
     * If this is sent, `assistantId`, `assistant`, `squadId`, `squad`, and `destination` are ignored.
     */
    error?: string;
}
