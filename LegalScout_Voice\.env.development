# Vapi Configuration
# ==================
# IMPORTANT: You need to replace the placeholder below with your actual Vapi API key
# Get your API key from: https://dashboard.vapi.ai/settings/api-keys
#
# 1. Log in to your Vapi account at https://dashboard.vapi.ai
# 2. Go to Settings > API Keys
# 3. Create a new API key if you don't have one
# 4. Copy the API key and paste it below
VITE_VAPI_PUBLIC_KEY=6734febc-fc65-4669-93b0-929b31ff6564

# Vapi API Base URL - Do not change this
VITE_VAPI_BASE_URL=https://api.vapi.ai

# Vapi Secret Key for server-side operations
VITE_VAPI_SECRET_KEY=6734febc-fc65-4669-93b0-929b31ff6564
VITE_VAPI_PRIVATE_KEY=6734febc-fc65-4669-93b0-929b31ff6564
VAPI_TOKEN=6734febc-fc65-4669-93b0-929b31ff6564

# Default assistant ID for testing
VITE_VAPI_ASSISTANT_ID=f9b97d13-f9c4-40af-a660-62ba5925ff2a

# Voice Configuration - Always use OpenAI Echo voice
VITE_VAPI_DEFAULT_VOICE_PROVIDER=openai
VITE_VAPI_DEFAULT_VOICE_ID=echo

# Development Mode Settings
NODE_ENV=development
VITE_DEV_MODE=false

# Other configurations from .env
VITE_SUPABASE_URL=https://utopqxsvudgrtiwenlzl.supabase.co
VITE_SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU
VITE_FALLBACK_MODE=false
VITE_APIFY_API_TOKEN=**********************************************

# Gmail OAuth Configuration
VITE_GMAIL_CLIENT_ID=your_gmail_client_id_here
VITE_GMAIL_REDIRECT_URI=http://localhost:5174/auth/callback