<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Ejection Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Ejection Fix</h1>
        <p>This test verifies that the "Meeting has ended" ejection error is fixed.</p>
        
        <div id="status" class="status info">
            Ready to test...
        </div>

        <button onclick="testEjectionFix()">
            🚀 Test Ejection Fix
        </button>
        
        <button onclick="testWithCorrectKey()">
            🔑 Test with Correct API Key
        </button>

        <button onclick="testDifferentAssistants()">
            🧪 Test Different Assistants
        </button>

        <button id="startCall" onclick="startTestCall()" disabled>
            📞 Start Test Call
        </button>
        
        <button id="stopCall" onclick="stopTestCall()" disabled>
            ⏹️ Stop Call
        </button>

        <div id="logs" class="log"></div>
    </div>

    <script type="module">
        // CRITICAL FIX: Based on MAKE_VAPI_WORK.md documentation
        // API Key for authentication (6734febc...) vs Assistant ID for referencing (310f0d43...)
        const CORRECT_API_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';  // API key for auth
        const ASSISTANT_ID = '310f0d43-27c2-47a5-a76d-e55171d024f7';     // Assistant ID for calls
        const DEFAULT_ASSISTANT_ID = 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865'; // From MAKE_VAPI_WORK.md

        // Log the configuration being used
        console.log('🔧 Test Configuration (from MAKE_VAPI_WORK.md):');
        console.log(`API Key: ${CORRECT_API_KEY.substring(0, 8)}...`);
        console.log(`Assistant ID: ${ASSISTANT_ID}`);
        console.log(`Default Assistant ID: ${DEFAULT_ASSISTANT_ID}`);
        
        let vapi = null;
        let isCallActive = false;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function updateButtons() {
            document.getElementById('startCall').disabled = !vapi || isCallActive;
            document.getElementById('stopCall').disabled = !isCallActive;
        }
        
        async function testEjectionFix() {
            updateStatus('Testing ejection fix...', 'info');
            document.getElementById('logs').innerHTML = '';
            
            log('🚀 Starting ejection fix test...');
            log('This test verifies the "Meeting has ended" error is fixed');
            
            // Test 1: Try with old key (should fail)
            log('\n📋 Test 1: Testing with OLD API key (should fail)');
            try {
                const { loadVapiSDK, createVapiInstance } = await import('/src/utils/vapiLoader.js');
                await loadVapiSDK();
                
                const oldVapi = await createVapiInstance(OLD_API_KEY);
                log('❌ Old API key should have failed but didn\'t', 'error');
                
            } catch (error) {
                log(`✅ Old API key correctly failed: ${error.message}`, 'success');
            }
            
            // Test 2: Try with correct key (should work)
            log('\n📋 Test 2: Testing with CORRECT API key');
            try {
                const { loadVapiSDK, createVapiInstance } = await import('/src/utils/vapiLoader.js');
                await loadVapiSDK();
                
                vapi = await createVapiInstance(CORRECT_API_KEY);
                
                // Set up event listeners to catch ejection errors
                vapi.on('error', (error) => {
                    log(`❌ Vapi error: ${error.errorMsg || error.message}`, 'error');
                    if (error.type === 'ejected' || error.errorMsg === 'Meeting has ended') {
                        log('❌ EJECTION ERROR DETECTED - Fix failed!', 'error');
                        updateStatus('Ejection error still occurring', 'error');
                    }
                });
                
                vapi.on('call-start', () => {
                    log('✅ Call started successfully - no ejection!', 'success');
                    isCallActive = true;
                    updateButtons();
                    updateStatus('Call active - ejection fix working!', 'success');
                });
                
                vapi.on('call-end', () => {
                    log('📞 Call ended normally', 'info');
                    isCallActive = false;
                    updateButtons();
                    updateStatus('Call ended normally', 'info');
                });
                
                log('✅ Vapi instance created with correct key', 'success');
                updateButtons();
                updateStatus('Ready to test calls', 'success');
                
            } catch (error) {
                log(`❌ Correct API key failed: ${error.message}`, 'error');
                updateStatus('Test failed', 'error');
            }
        }
        
        async function testWithCorrectKey() {
            updateStatus('Testing with correct key...', 'info');
            document.getElementById('logs').innerHTML = '';
            
            log('🔑 Testing with correct API key only...');
            
            try {
                const { loadVapiSDK, createVapiInstance } = await import('/src/utils/vapiLoader.js');
                await loadVapiSDK();
                
                vapi = await createVapiInstance(CORRECT_API_KEY);
                
                // Set up event listeners
                vapi.on('error', (error) => {
                    log(`❌ Vapi error: ${JSON.stringify(error)}`, 'error');
                });
                
                vapi.on('call-start', () => {
                    log('✅ Call started successfully!', 'success');
                    isCallActive = true;
                    updateButtons();
                });
                
                vapi.on('call-end', () => {
                    log('📞 Call ended', 'info');
                    isCallActive = false;
                    updateButtons();
                });
                
                log('✅ Vapi instance ready', 'success');
                updateButtons();
                updateStatus('Ready to test', 'success');
                
            } catch (error) {
                log(`❌ Failed: ${error.message}`, 'error');
                updateStatus('Test failed', 'error');
            }
        }
        
        async function startTestCall() {
            if (!vapi) {
                log('❌ Vapi not initialized', 'error');
                return;
            }
            
            updateStatus('Starting call...', 'info');
            log(`📞 Starting call with assistant: ${ASSISTANT_ID}`);
            
            try {
                // CRITICAL: Based on MAKE_VAPI_WORK.md - NO OVERRIDES for existing assistants
                // This is the exact pattern that prevents ejection errors
                log(`Starting call with assistant: ${ASSISTANT_ID} (NO OVERRIDES)`, 'info');
                await vapi.start(ASSISTANT_ID);  // No second parameter!
                log('✅ Call start request sent - using correct pattern from MAKE_VAPI_WORK.md', 'success');
                
            } catch (error) {
                log(`❌ Failed to start call: ${error.message}`, 'error');
                updateStatus(`Call failed: ${error.message}`, 'error');
            }
        }
        
        async function stopTestCall() {
            if (!vapi || !isCallActive) {
                log('❌ No active call to stop', 'error');
                return;
            }
            
            updateStatus('Stopping call...', 'info');
            log('⏹️ Stopping call...');
            
            try {
                await vapi.stop();
                log('✅ Call stopped', 'success');
                
            } catch (error) {
                log(`❌ Failed to stop call: ${error.message}`, 'error');
                updateStatus(`Stop failed: ${error.message}`, 'error');
            }
        }
        
        // Test with different assistant IDs from MAKE_VAPI_WORK.md
        async function testDifferentAssistants() {
            updateStatus('Testing different assistants...', 'info');
            document.getElementById('logs').innerHTML = '';

            log('🧪 Testing different assistant IDs from MAKE_VAPI_WORK.md...');

            const assistantsToTest = [
                { id: DEFAULT_ASSISTANT_ID, name: 'Default Assistant (from MAKE_VAPI_WORK.md)' },
                { id: ASSISTANT_ID, name: 'Current Assistant ID' },
                { id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', name: 'Assistant from .env.development' }
            ];

            for (const assistant of assistantsToTest) {
                log(`\n📋 Testing ${assistant.name}: ${assistant.id}`, 'info');

                try {
                    const { loadVapiSDK, createVapiInstance } = await import('/src/utils/vapiLoader.js');
                    const testVapi = await createVapiInstance(CORRECT_API_KEY);

                    // Set up event listeners
                    testVapi.on('call-start', () => {
                        log(`✅ ${assistant.name} - Call started successfully!`, 'success');
                    });

                    testVapi.on('call-end', (data) => {
                        log(`📞 ${assistant.name} - Call ended: ${JSON.stringify(data)}`, 'info');
                        if (data && data.reason && data.reason.includes('ejection')) {
                            log(`❌ ${assistant.name} - EJECTION DETECTED!`, 'error');
                        }
                    });

                    testVapi.on('error', (error) => {
                        log(`❌ ${assistant.name} - Error: ${JSON.stringify(error)}`, 'error');
                    });

                    // Start call with NO OVERRIDES (per MAKE_VAPI_WORK.md)
                    await testVapi.start(assistant.id);
                    log(`✅ ${assistant.name} - Call start request sent`, 'success');

                    // Wait 3 seconds then stop
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    testVapi.stop();
                    log(`⏹️ ${assistant.name} - Call stopped`, 'info');

                    // Wait before next test
                    await new Promise(resolve => setTimeout(resolve, 1000));

                } catch (error) {
                    log(`❌ ${assistant.name} - Failed: ${error.message}`, 'error');
                }
            }

            log('\n✅ Assistant testing completed', 'success');
            updateStatus('Assistant testing completed', 'success');
        }

        // Make functions available globally
        window.testEjectionFix = testEjectionFix;
        window.testWithCorrectKey = testWithCorrectKey;
        window.startTestCall = startTestCall;
        window.stopTestCall = stopTestCall;
        window.testDifferentAssistants = testDifferentAssistants;
    </script>
</body>
</html>
