/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface TesterPlan {
    /**
     * Pass a transient assistant to use for the test assistant.
     *
     * Make sure to write a detailed system prompt for a test assistant, and use the {{test.script}} variable to access the test script.
     */
    assistant?: Vapi.CreateAssistantDto;
    /**
     * Pass an assistant id that can be access
     *
     * Make sure to write a detailed system prompt for the test assistant, and use the {{test.script}} variable to access the test script.
     */
    assistantId?: string;
    /**
     * Add any assistant overrides to the test assistant.
     *
     * One use case is if you want to pass custom variables into the test using variableValues, that you can then access in the script
     * and rubric using {{varName}}.
     */
    assistantOverrides?: Vapi.AssistantOverrides;
}
