/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface UpdateCloudflareCredentialDto {
    /** Cloudflare Account Id. */
    accountId?: string;
    /** Cloudflare API Key / Token. */
    apiKey?: string;
    /** Cloudflare Account Email. */
    accountEmail?: string;
    /** This is the name of credential. This is just for your reference. */
    name?: string;
    /** This is the bucket plan that can be provided to store call artifacts in R2 */
    bucketPlan?: Vapi.CloudflareR2BucketPlan;
}
