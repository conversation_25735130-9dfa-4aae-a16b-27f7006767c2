<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Call Diagnostics - LegalScout Voice</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 4px solid #3498db;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 1.3em;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-idle {
            background: #95a5a6;
            color: white;
        }
        
        .status-connecting {
            background: #f39c12;
            color: white;
            animation: pulse 1.5s infinite;
        }
        
        .status-connected {
            background: #27ae60;
            color: white;
        }
        
        .status-error {
            background: #e74c3c;
            color: white;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 6px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 4px 0;
        }
        
        .log-timestamp {
            color: #95a5a6;
            margin-right: 10px;
        }
        
        .log-level-info {
            color: #3498db;
        }
        
        .log-level-success {
            color: #27ae60;
        }
        
        .log-level-error {
            color: #e74c3c;
        }
        
        .log-level-warning {
            color: #f39c12;
        }
        
        .config-display {
            background: #ecf0f1;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .metric-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .error-details {
            background: #fdf2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .error-title {
            color: #dc2626;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .error-message {
            color: #7f1d1d;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Vapi Call Diagnostics</h1>
            <p>Comprehensive testing and debugging for LegalScout Voice calls</p>
        </div>
        
        <div class="content">
            <!-- Configuration Section -->
            <div class="test-section">
                <h3>📋 Configuration Status</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="checkConfiguration()">Check Config</button>
                    <button class="btn btn-warning" onclick="testApiKeys()">Test API Keys</button>
                    <button class="btn btn-primary" onclick="loadAssistants()">Load Assistants</button>
                </div>
                <div id="config-status"></div>
                <div id="config-details" class="config-display" style="display: none;"></div>
            </div>
            
            <!-- SDK Testing Section -->
            <div class="test-section">
                <h3>🔌 SDK Integration Test</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="testVapiSDK()">Test SDK Loading</button>
                    <button class="btn btn-primary" onclick="createTestInstance()">Create Instance</button>
                    <button class="btn btn-success" onclick="testEventListeners()">Test Events</button>
                </div>
                <div id="sdk-status"></div>
            </div>
            
            <!-- Call Testing Section -->
            <div class="test-section">
                <h3>📞 Call Testing</h3>
                <div class="button-group">
                    <button class="btn btn-success" onclick="startDiagnosticCall()">Start Test Call</button>
                    <button class="btn btn-danger" onclick="stopCall()">Stop Call</button>
                    <button class="btn btn-warning" onclick="testMicrophone()">Test Microphone</button>
                </div>
                <div>
                    Call Status: <span id="call-status" class="status-indicator status-idle">IDLE</span>
                </div>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div id="volume-level" class="metric-value">0</div>
                        <div class="metric-label">Volume Level</div>
                    </div>
                    <div class="metric-card">
                        <div id="call-duration" class="metric-value">0s</div>
                        <div class="metric-label">Call Duration</div>
                    </div>
                    <div class="metric-card">
                        <div id="message-count" class="metric-value">0</div>
                        <div class="metric-label">Messages</div>
                    </div>
                </div>
            </div>
            
            <!-- Error Analysis Section -->
            <div class="test-section">
                <h3>🚨 Error Analysis</h3>
                <div class="button-group">
                    <button class="btn btn-warning" onclick="analyzeLastError()">Analyze Last Error</button>
                    <button class="btn btn-primary" onclick="exportDiagnostics()">Export Diagnostics</button>
                    <button class="btn btn-danger" onclick="clearLogs()">Clear Logs</button>
                </div>
                <div id="error-analysis"></div>
            </div>
            
            <!-- Live Logs -->
            <div class="test-section">
                <h3>📝 Live Diagnostic Logs</h3>
                <div id="diagnostic-logs" class="log-container">
                    <div class="log-entry">
                        <span class="log-timestamp">[Ready]</span>
                        <span class="log-level-info">Diagnostic tool initialized. Click any test button to begin.</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // Global state
        let vapiInstance = null;
        let callStartTime = null;
        let messageCount = 0;
        let lastError = null;
        let diagnosticData = {
            config: {},
            sdkTests: {},
            callTests: {},
            errors: []
        };

        // Logging system
        function addLog(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('diagnostic-logs');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level-${level}">${message}</span>
            `;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Store in diagnostic data
            diagnosticData.errors.push({
                timestamp,
                level,
                message
            });
        }

        // Configuration checking
        window.checkConfiguration = async function() {
            addLog('🔍 Checking configuration...', 'info');
            
            try {
                // Check environment variables
                const config = {
                    vapiPublicKey: import.meta.env.VITE_VAPI_PUBLIC_KEY,
                    vapiPrivateKey: import.meta.env.VITE_VAPI_PRIVATE_KEY,
                    defaultAssistantId: import.meta.env.VITE_VAPI_ASSISTANT_ID,
                    supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
                    supabaseKey: import.meta.env.VITE_SUPABASE_ANON_KEY
                };
                
                diagnosticData.config = config;
                
                let status = '✅ Configuration Status:\n';
                status += `• Vapi Public Key: ${config.vapiPublicKey ? '✅ Present' : '❌ Missing'}\n`;
                status += `• Vapi Private Key: ${config.vapiPrivateKey ? '✅ Present' : '❌ Missing'}\n`;
                status += `• Assistant ID: ${config.defaultAssistantId ? '✅ Present' : '❌ Missing'}\n`;
                status += `• Supabase URL: ${config.supabaseUrl ? '✅ Present' : '❌ Missing'}\n`;
                status += `• Supabase Key: ${config.supabaseKey ? '✅ Present' : '❌ Missing'}\n`;
                
                document.getElementById('config-status').innerHTML = `<pre>${status}</pre>`;
                document.getElementById('config-details').textContent = JSON.stringify(config, null, 2);
                document.getElementById('config-details').style.display = 'block';
                
                addLog('✅ Configuration check completed', 'success');
                
            } catch (error) {
                addLog(`❌ Configuration check failed: ${error.message}`, 'error');
                lastError = error;
            }
        };

        // API key testing
        window.testApiKeys = async function() {
            addLog('🔑 Testing API keys...', 'info');
            
            try {
                const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
                const privateKey = import.meta.env.VITE_VAPI_PRIVATE_KEY;
                
                if (!publicKey && !privateKey) {
                    throw new Error('No API keys found in environment');
                }
                
                // Test public key (if available)
                if (publicKey) {
                    addLog(`🔍 Testing public key: ${publicKey.substring(0, 8)}...`, 'info');
                    try {
                        const testInstance = await import('@vapi-ai/web').then(module => {
                            const Vapi = module.default || module;
                            return new Vapi(publicKey);
                        });
                        addLog('✅ Public key: Valid format, instance created', 'success');
                    } catch (error) {
                        addLog(`❌ Public key: ${error.message}`, 'error');
                    }
                }
                
                // Test private key with API call
                if (privateKey) {
                    addLog(`🔍 Testing private key: ${privateKey.substring(0, 8)}...`, 'info');
                    try {
                        const response = await fetch('https://api.vapi.ai/assistant', {
                            headers: {
                                'Authorization': `Bearer ${privateKey}`,
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (response.ok) {
                            const assistants = await response.json();
                            addLog(`✅ Private key: Valid, found ${assistants.length} assistants`, 'success');
                        } else {
                            addLog(`❌ Private key: HTTP ${response.status} - ${response.statusText}`, 'error');
                        }
                    } catch (error) {
                        addLog(`❌ Private key test failed: ${error.message}`, 'error');
                    }
                }
                
            } catch (error) {
                addLog(`❌ API key testing failed: ${error.message}`, 'error');
                lastError = error;
            }
        };

        // Load assistants
        window.loadAssistants = async function() {
            addLog('🤖 Loading assistants...', 'info');
            
            try {
                const privateKey = import.meta.env.VITE_VAPI_PRIVATE_KEY;
                if (!privateKey) {
                    throw new Error('Private key required to load assistants');
                }
                
                const response = await fetch('https://api.vapi.ai/assistant', {
                    headers: {
                        'Authorization': `Bearer ${privateKey}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const assistants = await response.json();
                addLog(`✅ Loaded ${assistants.length} assistants`, 'success');
                
                assistants.forEach((assistant, index) => {
                    addLog(`  ${index + 1}. ${assistant.name} (${assistant.id})`, 'info');
                });
                
                diagnosticData.assistants = assistants;
                
            } catch (error) {
                addLog(`❌ Failed to load assistants: ${error.message}`, 'error');
                lastError = error;
            }
        };

        // SDK Testing
        window.testVapiSDK = async function() {
            addLog('🔌 Testing Vapi SDK loading...', 'info');

            try {
                // Test ES module import
                addLog('📦 Importing @vapi-ai/web module...', 'info');
                const VapiModule = await import('@vapi-ai/web');
                const Vapi = VapiModule.default || VapiModule;

                if (typeof Vapi !== 'function') {
                    throw new Error(`Expected Vapi to be a function, got ${typeof Vapi}`);
                }

                addLog('✅ Vapi SDK imported successfully', 'success');
                addLog(`📋 Vapi constructor type: ${typeof Vapi}`, 'info');

                // Test if we can access Vapi methods
                const testKey = 'test-key-12345';
                const testInstance = new Vapi(testKey);

                const requiredMethods = ['start', 'stop', 'on', 'off'];
                const availableMethods = requiredMethods.filter(method =>
                    typeof testInstance[method] === 'function'
                );

                addLog(`✅ Available methods: ${availableMethods.join(', ')}`, 'success');

                if (availableMethods.length === requiredMethods.length) {
                    addLog('✅ All required methods available', 'success');
                } else {
                    const missing = requiredMethods.filter(method =>
                        !availableMethods.includes(method)
                    );
                    addLog(`⚠️ Missing methods: ${missing.join(', ')}`, 'warning');
                }

                diagnosticData.sdkTests.import = 'success';
                diagnosticData.sdkTests.methods = availableMethods;

            } catch (error) {
                addLog(`❌ SDK test failed: ${error.message}`, 'error');
                diagnosticData.sdkTests.import = 'failed';
                lastError = error;
            }
        };

        // Create test instance
        window.createTestInstance = async function() {
            addLog('🏗️ Creating Vapi test instance...', 'info');

            try {
                const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
                if (!publicKey) {
                    throw new Error('No public key available for instance creation');
                }

                const VapiModule = await import('@vapi-ai/web');
                const Vapi = VapiModule.default || VapiModule;

                vapiInstance = new Vapi(publicKey);
                addLog(`✅ Vapi instance created with key: ${publicKey.substring(0, 8)}...`, 'success');

                // Test instance properties
                addLog(`📋 Instance type: ${typeof vapiInstance}`, 'info');
                addLog(`📋 Instance constructor: ${vapiInstance.constructor.name}`, 'info');

                document.getElementById('sdk-status').innerHTML =
                    '<span style="color: green;">✅ Instance Ready</span>';

                diagnosticData.sdkTests.instanceCreation = 'success';

            } catch (error) {
                addLog(`❌ Instance creation failed: ${error.message}`, 'error');
                document.getElementById('sdk-status').innerHTML =
                    '<span style="color: red;">❌ Instance Failed</span>';
                diagnosticData.sdkTests.instanceCreation = 'failed';
                lastError = error;
            }
        };

        // Test event listeners
        window.testEventListeners = function() {
            addLog('🎧 Testing event listeners...', 'info');

            if (!vapiInstance) {
                addLog('❌ No Vapi instance available. Create instance first.', 'error');
                return;
            }

            try {
                const events = ['call-start', 'call-end', 'message', 'error', 'volume-level'];
                let successCount = 0;

                events.forEach(eventName => {
                    try {
                        vapiInstance.on(eventName, () => {
                            addLog(`🎯 Event fired: ${eventName}`, 'info');
                        });
                        successCount++;
                        addLog(`✅ Event listener added: ${eventName}`, 'success');
                    } catch (error) {
                        addLog(`❌ Failed to add listener for ${eventName}: ${error.message}`, 'error');
                    }
                });

                addLog(`✅ Event listeners test completed: ${successCount}/${events.length} successful`, 'success');
                diagnosticData.sdkTests.eventListeners = `${successCount}/${events.length}`;

            } catch (error) {
                addLog(`❌ Event listener test failed: ${error.message}`, 'error');
                lastError = error;
            }
        };

        // Start diagnostic call
        window.startDiagnosticCall = async function() {
            addLog('📞 Starting diagnostic call...', 'info');

            try {
                if (!vapiInstance) {
                    addLog('🏗️ No instance available, creating one...', 'info');
                    await createTestInstance();
                }

                if (!vapiInstance) {
                    throw new Error('Failed to create Vapi instance');
                }

                const assistantId = import.meta.env.VITE_VAPI_ASSISTANT_ID;
                if (!assistantId) {
                    throw new Error('No assistant ID configured');
                }

                // Set up comprehensive event listeners
                setupCallEventListeners();

                // Update status
                updateCallStatus('connecting');
                callStartTime = Date.now();

                addLog(`🎯 Starting call with assistant: ${assistantId}`, 'info');

                // Start the call
                const result = await vapiInstance.start(assistantId);
                addLog(`✅ Call start result: ${JSON.stringify(result)}`, 'success');

            } catch (error) {
                addLog(`❌ Call start failed: ${error.message}`, 'error');
                addLog(`📊 Error details: ${JSON.stringify(error, null, 2)}`, 'error');
                updateCallStatus('error');
                lastError = error;

                // Show detailed error analysis
                showErrorAnalysis(error);
            }
        };

        // Setup call event listeners
        function setupCallEventListeners() {
            if (!vapiInstance) return;

            vapiInstance.on('call-start', () => {
                addLog('🟢 Call started successfully', 'success');
                updateCallStatus('connected');
            });

            vapiInstance.on('call-end', () => {
                addLog('🔴 Call ended', 'info');
                updateCallStatus('idle');
                callStartTime = null;
            });

            vapiInstance.on('message', (message) => {
                messageCount++;
                addLog(`💬 Message received: ${JSON.stringify(message)}`, 'info');
                document.getElementById('message-count').textContent = messageCount;
            });

            vapiInstance.on('error', (error) => {
                addLog(`🚨 Call error: ${error.message || JSON.stringify(error)}`, 'error');
                lastError = error;
                updateCallStatus('error');
            });

            vapiInstance.on('volume-level', (level) => {
                document.getElementById('volume-level').textContent = Math.round(level * 100);
            });

            vapiInstance.on('speech-start', () => {
                addLog('🗣️ Speech started', 'info');
            });

            vapiInstance.on('speech-end', () => {
                addLog('🤐 Speech ended', 'info');
            });
        }

        // Update call status
        function updateCallStatus(status) {
            const statusElement = document.getElementById('call-status');
            statusElement.textContent = status.toUpperCase();
            statusElement.className = `status-indicator status-${status}`;
        }

        // Stop call
        window.stopCall = function() {
            if (!vapiInstance) {
                addLog('❌ No active call to stop', 'warning');
                return;
            }

            try {
                vapiInstance.stop();
                addLog('🛑 Call stop requested', 'info');
                updateCallStatus('idle');
            } catch (error) {
                addLog(`❌ Error stopping call: ${error.message}`, 'error');
            }
        };

        // Test microphone
        window.testMicrophone = async function() {
            addLog('🎤 Testing microphone access...', 'info');

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                addLog('✅ Microphone access granted', 'success');

                // Test audio devices
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');

                addLog(`🎧 Found ${audioInputs.length} audio input devices:`, 'info');
                audioInputs.forEach((device, index) => {
                    addLog(`  ${index + 1}. ${device.label || 'Unknown Device'}`, 'info');
                });

                // Stop the test stream
                stream.getTracks().forEach(track => track.stop());

            } catch (error) {
                addLog(`❌ Microphone test failed: ${error.message}`, 'error');
                lastError = error;
            }
        };

        // Show error analysis
        function showErrorAnalysis(error) {
            const analysisDiv = document.getElementById('error-analysis');

            let analysis = `
                <div class="error-details">
                    <div class="error-title">🚨 Error Analysis</div>
                    <div class="error-message">
Error Type: ${error.constructor.name}
Message: ${error.message}
Stack: ${error.stack || 'No stack trace available'}

Common Solutions:
• Check API key validity in Vapi dashboard
• Verify assistant ID exists and is accessible
• Ensure microphone permissions are granted
• Check network connectivity
• Verify Vapi service status
                    </div>
                </div>
            `;

            analysisDiv.innerHTML = analysis;
        }

        // Analyze last error
        window.analyzeLastError = function() {
            if (!lastError) {
                addLog('ℹ️ No recent errors to analyze', 'info');
                return;
            }

            addLog('🔍 Analyzing last error...', 'info');
            showErrorAnalysis(lastError);
        };

        // Export diagnostics
        window.exportDiagnostics = function() {
            const data = {
                timestamp: new Date().toISOString(),
                diagnosticData,
                lastError: lastError ? {
                    message: lastError.message,
                    stack: lastError.stack,
                    type: lastError.constructor.name
                } : null
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `vapi-diagnostics-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);

            addLog('📁 Diagnostics exported', 'success');
        };

        // Clear logs
        window.clearLogs = function() {
            document.getElementById('diagnostic-logs').innerHTML = `
                <div class="log-entry">
                    <span class="log-timestamp">[Cleared]</span>
                    <span class="log-level-info">Logs cleared. Ready for new tests.</span>
                </div>
            `;
            messageCount = 0;
            document.getElementById('message-count').textContent = '0';
            addLog('🧹 Logs cleared', 'info');
        };

        // Update call duration
        setInterval(() => {
            if (callStartTime) {
                const duration = Math.floor((Date.now() - callStartTime) / 1000);
                document.getElementById('call-duration').textContent = `${duration}s`;
            }
        }, 1000);

        // Make functions globally available
        window.addLog = addLog;
        window.diagnosticData = diagnosticData;

    </script>
</body>
</html>
