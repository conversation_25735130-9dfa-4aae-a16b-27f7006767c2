#!/usr/bin/env node

/**
 * Comprehensive Vapi Ejection Debugging Script
 * This script will test all common causes of "Meeting ended due to ejection"
 */

import https from 'https';

const API_KEY = '6734febc-fc65-4669-93b0-929b31ff6564'; // The working API key
const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

console.log('🔍 Comprehensive Vapi Ejection Debugging');
console.log('==========================================\n');

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'LegalScout-Debug/1.0',
        ...options.headers
      }
    }, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed, raw: data });
        } catch (e) {
          resolve({ status: res.statusCode, data: null, raw: data });
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    req.end();
  });
}

// Test 1: Verify API Key and Account Status
async function testAccountStatus() {
  console.log('📋 Test 1: Account Status and API Key Validation');
  console.log('-'.repeat(50));

  try {
    // Try multiple endpoints to verify API key
    const endpoints = [
      { name: 'Account Info', url: 'https://api.vapi.ai/account' },
      { name: 'Assistants List', url: 'https://api.vapi.ai/assistant' },
      { name: 'Phone Numbers', url: 'https://api.vapi.ai/phone-number' }
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await makeRequest(endpoint.url);
        if (response.status === 200) {
          console.log(`✅ ${endpoint.name}: Working`);
          if (endpoint.name === 'Account Info' && response.data) {
            console.log(`   Account: ${response.data.email || response.data.id || 'Valid'}`);
          }
        } else {
          console.log(`❌ ${endpoint.name}: HTTP ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ ${endpoint.name}: ${error.message}`);
      }
    }
  } catch (error) {
    console.log(`❌ Account test failed: ${error.message}`);
  }
  console.log('');
}

// Test 2: Verify Assistant Configuration
async function testAssistantConfig() {
  console.log('📋 Test 2: Assistant Configuration');
  console.log('-'.repeat(50));

  try {
    const response = await makeRequest(`https://api.vapi.ai/assistant/${ASSISTANT_ID}`);
    
    if (response.status === 200 && response.data) {
      const assistant = response.data;
      console.log(`✅ Assistant found: ${assistant.name || 'Unnamed'}`);
      
      // Check critical configuration
      const checks = [
        { name: 'Voice Provider', value: assistant.voice?.provider, required: true },
        { name: 'Voice ID', value: assistant.voice?.voiceId, required: true },
        { name: 'Model Provider', value: assistant.model?.provider, required: true },
        { name: 'Model Name', value: assistant.model?.model, required: true },
        { name: 'First Message', value: assistant.firstMessage, required: false },
        { name: 'Transcriber', value: assistant.transcriber?.provider, required: true }
      ];

      checks.forEach(check => {
        if (check.value) {
          console.log(`✅ ${check.name}: ${check.value}`);
        } else if (check.required) {
          console.log(`❌ ${check.name}: Missing (CRITICAL)`);
        } else {
          console.log(`⚠️  ${check.name}: Not set`);
        }
      });

      // Check for common problematic configurations
      if (assistant.voice?.provider === 'openai' && !assistant.voice?.voiceId) {
        console.log('❌ ISSUE: OpenAI voice provider requires voiceId');
      }
      
      if (assistant.model?.provider === 'openai' && !assistant.model?.model) {
        console.log('❌ ISSUE: OpenAI model provider requires model specification');
      }

    } else {
      console.log(`❌ Assistant not accessible: HTTP ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ Assistant test failed: ${error.message}`);
  }
  console.log('');
}

// Test 3: Test Call Creation (without actually making a call)
async function testCallCreation() {
  console.log('📋 Test 3: Call Creation Test');
  console.log('-'.repeat(50));

  try {
    // Test creating a call configuration
    const callConfig = {
      assistantId: ASSISTANT_ID,
      type: 'webCall' // For web-based calls
    };

    const response = await makeRequest('https://api.vapi.ai/call', {
      method: 'POST',
      body: callConfig
    });

    if (response.status === 201 || response.status === 200) {
      console.log('✅ Call creation: Successful');
      if (response.data?.id) {
        console.log(`   Call ID: ${response.data.id}`);
        
        // Immediately end the test call
        try {
          await makeRequest(`https://api.vapi.ai/call/${response.data.id}`, {
            method: 'PATCH',
            body: { status: 'ended' }
          });
          console.log('✅ Test call ended successfully');
        } catch (endError) {
          console.log('⚠️  Could not end test call (not critical)');
        }
      }
    } else {
      console.log(`❌ Call creation failed: HTTP ${response.status}`);
      if (response.data?.message) {
        console.log(`   Error: ${response.data.message}`);
      }
    }
  } catch (error) {
    console.log(`❌ Call creation test failed: ${error.message}`);
  }
  console.log('');
}

// Test 4: Check for Common Configuration Issues
async function testCommonIssues() {
  console.log('📋 Test 4: Common Configuration Issues');
  console.log('-'.repeat(50));

  const issues = [];

  // Check if using old API endpoints
  try {
    const oldEndpoint = await makeRequest('https://api.vapi.ai/assistants'); // Note: plural
    if (oldEndpoint.status !== 404) {
      issues.push('Using deprecated /assistants endpoint (should be /assistant)');
    }
  } catch (e) {
    // Expected to fail
  }

  // Check for rate limiting
  try {
    const rapidRequests = await Promise.all([
      makeRequest('https://api.vapi.ai/assistant'),
      makeRequest('https://api.vapi.ai/assistant'),
      makeRequest('https://api.vapi.ai/assistant'),
      makeRequest('https://api.vapi.ai/assistant'),
      makeRequest('https://api.vapi.ai/assistant')
    ]);

    const rateLimited = rapidRequests.some(r => r.status === 429);
    if (rateLimited) {
      issues.push('Rate limiting detected - may cause call failures');
    } else {
      console.log('✅ No rate limiting issues detected');
    }
  } catch (error) {
    issues.push(`Network issues detected: ${error.message}`);
  }

  if (issues.length === 0) {
    console.log('✅ No common configuration issues found');
  } else {
    issues.forEach(issue => console.log(`❌ ${issue}`));
  }
  console.log('');
}

// Test 5: Environment and Browser Compatibility Check
async function testEnvironmentCompatibility() {
  console.log('📋 Test 5: Environment Compatibility');
  console.log('-'.repeat(50));

  // Check Node.js version
  console.log(`✅ Node.js version: ${process.version}`);
  
  // Check if we can access Vapi's CDN
  try {
    const cdnResponse = await makeRequest('https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.js');
    if (cdnResponse.status === 200) {
      console.log('✅ Vapi CDN accessible');
    } else {
      console.log('❌ Vapi CDN not accessible - may cause SDK loading issues');
    }
  } catch (error) {
    console.log('❌ CDN access failed - check network/firewall');
  }

  console.log('');
}

// Main execution
async function runAllTests() {
  console.log(`🔑 Using API Key: ${API_KEY.substring(0, 8)}...`);
  console.log(`🤖 Testing Assistant: ${ASSISTANT_ID.substring(0, 8)}...\n`);

  await testAccountStatus();
  await testAssistantConfig();
  await testCallCreation();
  await testCommonIssues();
  await testEnvironmentCompatibility();

  console.log('🎯 SUMMARY AND RECOMMENDATIONS');
  console.log('='.repeat(50));
  console.log('If all tests above passed but you still get ejection errors:');
  console.log('');
  console.log('1. 🌐 Browser Issues:');
  console.log('   - Clear browser cache and cookies');
  console.log('   - Try in incognito/private mode');
  console.log('   - Check browser console for JavaScript errors');
  console.log('');
  console.log('2. 🎤 Audio Issues:');
  console.log('   - Ensure microphone permissions are granted');
  console.log('   - Check if other apps are using the microphone');
  console.log('   - Try with headphones to avoid echo');
  console.log('');
  console.log('3. 🔧 SDK Issues:');
  console.log('   - Ensure Vapi Web SDK is loaded correctly');
  console.log('   - Check for version conflicts');
  console.log('   - Verify event listeners are set up properly');
  console.log('');
  console.log('4. 🌍 Network Issues:');
  console.log('   - Check firewall settings');
  console.log('   - Verify WebRTC is not blocked');
  console.log('   - Test on different network connection');
  console.log('');
  console.log('✅ Diagnostic completed!');
}

runAllTests().catch(error => {
  console.error('💥 Diagnostic failed:', error);
  process.exit(1);
});
