<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Ejection Diagnosis</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        .issue {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .recommendation {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Vapi "Meeting ended due to ejection" Diagnosis</h1>
        <p>This tool will help diagnose why your Vapi calls are being ejected and provide specific solutions.</p>
        
        <div id="status" class="status info">
            Ready to run diagnosis...
        </div>

        <button id="runDiagnosis" onclick="runDiagnosis()">
            🔍 Run Full Diagnosis
        </button>
        
        <button id="testApiKey" onclick="testApiKey()">
            🔑 Test API Key
        </button>
        
        <button id="testAssistant" onclick="testAssistant()">
            🤖 Test Assistant
        </button>

        <button id="testBothKeys" onclick="testBothKeys()">
            🔑🔑 Test Both Keys
        </button>

        <button id="testAssistantConfig" onclick="testAssistantConfig()">
            🤖 Test Assistant Config
        </button>

        <button id="testCallCreation" onclick="testCallCreation()">
            📞 Test Call Creation
        </button>

        <div id="results" style="margin-top: 20px;"></div>
    </div>

    <script>
        // Test both keys to see which one actually works as an API key
        const KEY_1 = '310f0d43-27c2-47a5-a76d-e55171d024f7'; // Currently used as API key
        const KEY_2 = '6734febc-fc65-4669-93b0-929b31ff6564'; // Secret key from env
        const DEFAULT_ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function addResult(title, content, type = 'info') {
            const resultsEl = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="status ${type}">${content}</div>
            `;
            resultsEl.appendChild(resultDiv);
        }

        function addIssue(issue) {
            const resultsEl = document.getElementById('results');
            const issueDiv = document.createElement('div');
            issueDiv.className = 'issue';
            issueDiv.textContent = '❌ ' + issue;
            resultsEl.appendChild(issueDiv);
        }

        function addRecommendation(recommendation) {
            const resultsEl = document.getElementById('results');
            const recDiv = document.createElement('div');
            recDiv.className = 'recommendation';
            recDiv.textContent = '💡 ' + recommendation;
            resultsEl.appendChild(recDiv);
        }

        async function testApiKey() {
            updateStatus('Testing API key...', 'info');
            document.getElementById('results').innerHTML = '';

            // Test the currently configured key
            const currentKey = KEY_1;

            // Check API key format
            if (!currentKey) {
                addIssue('VITE_VAPI_PUBLIC_KEY is not set');
                addRecommendation('Set VITE_VAPI_PUBLIC_KEY in your .env file');
                updateStatus('API key test failed', 'error');
                return false;
            }

            if (currentKey.length !== 36) {
                addIssue('API key format is invalid (should be 36 characters UUID format)');
                addRecommendation('Verify your Vapi public key is correct');
                updateStatus('API key test failed', 'error');
                return false;
            }

            addResult('API Key Format', 'Valid UUID format ✅', 'success');
            updateStatus('API key format is valid', 'success');
            return true;
        }

        async function testBothKeys() {
            updateStatus('Testing both keys to determine which is the real API key...', 'info');
            document.getElementById('results').innerHTML = '';

            const keys = [
                { name: 'Key 1 (Currently used as API key)', value: KEY_1, description: '310f0d43-27c2-47a5-a76d-e55171d024f7' },
                { name: 'Key 2 (Secret key from env)', value: KEY_2, description: '6734febc-fc65-4669-93b0-929b31ff6564' }
            ];

            let workingKey = null;

            for (const key of keys) {
                addResult(`Testing ${key.name}`, `Testing ${key.description}...`, 'info');

                try {
                    // Test 1: Try to get account info (basic API test)
                    const accountResponse = await fetch('https://api.vapi.ai/account', {
                        headers: {
                            'Authorization': `Bearer ${key.value}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (accountResponse.ok) {
                        const accountData = await accountResponse.json();
                        addResult(`${key.name} - Account Test`, `✅ SUCCESS: Got account data (${accountData.email || 'account found'})`, 'success');
                        workingKey = key;
                    } else if (accountResponse.status === 401) {
                        addResult(`${key.name} - Account Test`, `❌ FAILED: Unauthorized (401) - Not a valid API key`, 'error');
                    } else if (accountResponse.status === 404) {
                        addResult(`${key.name} - Account Test`, `❌ FAILED: Not found (404) - Endpoint may not exist`, 'warning');
                    } else {
                        addResult(`${key.name} - Account Test`, `❌ FAILED: HTTP ${accountResponse.status}`, 'error');
                    }

                    // Test 2: Try to list assistants
                    const assistantsResponse = await fetch('https://api.vapi.ai/assistant', {
                        headers: {
                            'Authorization': `Bearer ${key.value}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (assistantsResponse.ok) {
                        const assistants = await assistantsResponse.json();
                        const count = Array.isArray(assistants) ? assistants.length : 'unknown';
                        addResult(`${key.name} - Assistants Test`, `✅ SUCCESS: Can list assistants (${count} found)`, 'success');
                        if (!workingKey) workingKey = key;
                    } else if (assistantsResponse.status === 401) {
                        addResult(`${key.name} - Assistants Test`, `❌ FAILED: Unauthorized (401) - Not a valid API key`, 'error');
                    } else {
                        addResult(`${key.name} - Assistants Test`, `❌ FAILED: HTTP ${assistantsResponse.status}`, 'error');
                    }

                } catch (error) {
                    addResult(`${key.name} - Error`, `❌ NETWORK ERROR: ${error.message}`, 'error');
                }

                // Add separator
                const resultsEl = document.getElementById('results');
                const separator = document.createElement('hr');
                separator.style.margin = '20px 0';
                resultsEl.appendChild(separator);
            }

            // Summary
            if (workingKey) {
                addResult('🎉 CONCLUSION', `${workingKey.name} is the REAL API KEY!`, 'success');
                addRecommendation(`Use ${workingKey.description} as your VITE_VAPI_PUBLIC_KEY`);

                if (workingKey.value === KEY_2) {
                    addRecommendation('Update your .env file: VITE_VAPI_PUBLIC_KEY=' + KEY_2);
                    addRecommendation('The current key (310f0d43...) appears to be an assistant ID, not an API key');
                }

                updateStatus(`Found working API key: ${workingKey.name}`, 'success');
            } else {
                addResult('❌ CONCLUSION', 'Neither key works as an API key', 'error');
                addRecommendation('You may need to generate a new API key from your Vapi dashboard');
                addRecommendation('Go to https://dashboard.vapi.ai → Settings → API Keys');
                updateStatus('No working API key found', 'error');
            }
        }

        async function testAssistant() {
            updateStatus('Testing assistant accessibility...', 'info');

            try {
                const response = await fetch(`https://api.vapi.ai/assistant/${DEFAULT_ASSISTANT_ID}`, {
                    headers: {
                        'Authorization': `Bearer ${KEY_1}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const assistant = await response.json();
                    addResult('Assistant Status', `Assistant "${assistant.name}" is accessible ✅`, 'success');
                    updateStatus('Assistant test passed', 'success');
                    return true;
                } else if (response.status === 401) {
                    addIssue('Unauthorized access to assistant - API key is invalid');
                    addRecommendation('Verify your Vapi API key has correct permissions');
                    updateStatus('Assistant test failed - unauthorized', 'error');
                    return false;
                } else if (response.status === 404) {
                    addIssue('Assistant not found - may have been deleted or ID is incorrect');
                    addRecommendation('Verify the assistant ID exists in your Vapi dashboard');
                    updateStatus('Assistant test failed - not found', 'error');
                    return false;
                } else {
                    addIssue(`Assistant check failed with status: ${response.status}`);
                    updateStatus('Assistant test failed', 'error');
                    return false;
                }
            } catch (error) {
                addIssue(`Error checking assistant: ${error.message}`);
                updateStatus('Assistant test failed', 'error');
                return false;
            }
        }

        async function testMicrophone() {
            updateStatus('Testing microphone access...', 'info');
            
            try {
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    addIssue('Microphone access not available in this browser');
                    addRecommendation('Use a modern browser that supports WebRTC');
                    return false;
                }

                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop()); // Clean up
                
                addResult('Microphone Access', 'Available ✅', 'success');
                return true;
            } catch (error) {
                addIssue(`Microphone access error: ${error.message}`);
                addRecommendation('Grant microphone permissions and ensure no other app is using it');
                return false;
            }
        }

        async function testNetwork() {
            updateStatus('Testing network connectivity...', 'info');
            
            try {
                const response = await fetch('https://api.vapi.ai/health', {
                    method: 'GET'
                });

                if (response.ok) {
                    addResult('Network Connectivity', 'Vapi API is reachable ✅', 'success');
                    return true;
                } else {
                    addIssue('Vapi API is not responding properly');
                    addRecommendation('Check your internet connection and firewall settings');
                    return false;
                }
            } catch (error) {
                addIssue(`Network connectivity issue: ${error.message}`);
                addRecommendation('Check your internet connection and firewall settings');
                return false;
            }
        }

        async function testAssistantConfig() {
            updateStatus('Testing assistant configuration...', 'info');
            document.getElementById('results').innerHTML = '';

            try {
                const response = await fetch(`https://api.vapi.ai/assistant/${DEFAULT_ASSISTANT_ID}`, {
                    headers: {
                        'Authorization': `Bearer ${KEY_2}`, // Use the working API key
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const assistant = response.json ? await response.json() : null;
                    addResult('Assistant Found', `✅ Assistant "${assistant?.name || 'Found'}" is accessible`, 'success');

                    if (assistant) {
                        // Check critical configuration
                        const checks = [
                            { name: 'Voice Provider', value: assistant.voice?.provider, required: true },
                            { name: 'Voice ID', value: assistant.voice?.voiceId, required: true },
                            { name: 'Model Provider', value: assistant.model?.provider, required: true },
                            { name: 'Model Name', value: assistant.model?.model, required: true },
                            { name: 'Transcriber', value: assistant.transcriber?.provider, required: true }
                        ];

                        checks.forEach(check => {
                            if (check.value) {
                                addResult(check.name, `✅ ${check.value}`, 'success');
                            } else if (check.required) {
                                addIssue(`${check.name} is missing (CRITICAL)`);
                                addRecommendation(`Configure ${check.name} in your Vapi dashboard`);
                            }
                        });

                        // Check for problematic configurations
                        if (assistant.voice?.provider === 'openai' && !assistant.voice?.voiceId) {
                            addIssue('OpenAI voice provider requires voiceId');
                            addRecommendation('Set a valid OpenAI voice ID (e.g., "alloy", "echo", "fable")');
                        }
                    }

                    updateStatus('Assistant configuration test completed', 'success');
                } else {
                    addIssue(`Assistant not accessible: HTTP ${response.status}`);
                    updateStatus('Assistant test failed', 'error');
                }
            } catch (error) {
                addIssue(`Error checking assistant: ${error.message}`);
                updateStatus('Assistant test failed', 'error');
            }
        }

        async function testCallCreation() {
            updateStatus('Testing call creation...', 'info');

            try {
                const callConfig = {
                    assistantId: DEFAULT_ASSISTANT_ID,
                    type: 'webCall'
                };

                const response = await fetch('https://api.vapi.ai/call', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${KEY_2}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(callConfig)
                });

                if (response.ok) {
                    const callData = await response.json();
                    addResult('Call Creation', '✅ Can create calls successfully', 'success');

                    if (callData.id) {
                        // Immediately end the test call
                        try {
                            await fetch(`https://api.vapi.ai/call/${callData.id}`, {
                                method: 'PATCH',
                                headers: {
                                    'Authorization': `Bearer ${KEY_2}`,
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ status: 'ended' })
                            });
                            addResult('Call Cleanup', '✅ Test call ended successfully', 'success');
                        } catch (e) {
                            addResult('Call Cleanup', '⚠️ Could not end test call (not critical)', 'warning');
                        }
                    }
                    updateStatus('Call creation test passed', 'success');
                } else {
                    const errorData = await response.json().catch(() => null);
                    addIssue(`Call creation failed: HTTP ${response.status}`);
                    if (errorData?.message) {
                        addIssue(`Error details: ${errorData.message}`);
                    }
                    updateStatus('Call creation test failed', 'error');
                }
            } catch (error) {
                addIssue(`Call creation error: ${error.message}`);
                updateStatus('Call creation test failed', 'error');
            }
        }

        async function runDiagnosis() {
            updateStatus('Running comprehensive diagnosis...', 'info');
            document.getElementById('results').innerHTML = '';

            const tests = [
                { name: 'API Key', test: testApiKey },
                { name: 'Assistant Config', test: testAssistantConfig },
                { name: 'Call Creation', test: testCallCreation },
                { name: 'Assistant Access', test: testAssistant },
                { name: 'Microphone', test: testMicrophone },
                { name: 'Network', test: testNetwork }
            ];

            let allPassed = true;

            for (const { name, test } of tests) {
                updateStatus(`Testing ${name}...`, 'info');
                const passed = await test();
                if (!passed) allPassed = false;
                await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between tests
            }

            if (allPassed) {
                updateStatus('All tests passed! Your Vapi configuration should work.', 'success');
                addRecommendation('If you\'re still experiencing ejection issues, check the browser console for additional error messages');
                addRecommendation('Try clearing browser cache and testing in incognito mode');
                addRecommendation('Check for JavaScript errors in browser console during call attempts');
            } else {
                updateStatus('Issues found! Please address the problems above.', 'error');
                addRecommendation('Fix the critical issues first, then test your Vapi calls again');
            }
        }

        // Auto-run diagnosis on page load
        window.addEventListener('load', () => {
            setTimeout(runDiagnosis, 1000);
        });
    </script>
</body>
</html>
