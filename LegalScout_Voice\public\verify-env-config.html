<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Environment Configuration Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .config-item {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .config-item.success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .config-item.warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
        }
        .config-item.error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .config-value {
            font-family: monospace;
            background-color: #e9ecef;
            padding: 5px;
            border-radius: 3px;
            margin-top: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Environment Configuration Verification</h1>
        <p>This tool verifies that your .env.development file is being read correctly by the test tools.</p>
        
        <button onclick="verifyConfig()">🔍 Verify Configuration</button>
        <button onclick="testApiConnection()">🌐 Test API Connection</button>
        
        <div id="results"></div>
    </div>

    <script type="module">
        function createConfigItem(label, value, status = 'info') {
            return `
                <div class="config-item ${status}">
                    <strong>${label}:</strong>
                    <div class="config-value">${value}</div>
                </div>
            `;
        }

        function verifyConfig() {
            const resultsDiv = document.getElementById('results');
            let html = '<h2>📋 Configuration Status</h2>';
            
            // Check environment variables
            const configs = [
                {
                    label: 'VITE_VAPI_PUBLIC_KEY',
                    value: import.meta.env.VITE_VAPI_PUBLIC_KEY,
                    expected: '6734febc-fc65-4669-93b0-929b31ff6564'
                },
                {
                    label: 'VITE_VAPI_SECRET_KEY',
                    value: import.meta.env.VITE_VAPI_SECRET_KEY,
                    expected: '6734febc-fc65-4669-93b0-929b31ff6564'
                },
                {
                    label: 'VITE_VAPI_ASSISTANT_ID',
                    value: import.meta.env.VITE_VAPI_ASSISTANT_ID,
                    expected: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'
                },
                {
                    label: 'VITE_VAPI_BASE_URL',
                    value: import.meta.env.VITE_VAPI_BASE_URL,
                    expected: 'https://api.vapi.ai'
                },
                {
                    label: 'VITE_VAPI_DEFAULT_VOICE_PROVIDER',
                    value: import.meta.env.VITE_VAPI_DEFAULT_VOICE_PROVIDER,
                    expected: 'openai'
                },
                {
                    label: 'VITE_VAPI_DEFAULT_VOICE_ID',
                    value: import.meta.env.VITE_VAPI_DEFAULT_VOICE_ID,
                    expected: 'echo'
                }
            ];
            
            configs.forEach(config => {
                let status = 'error';
                let displayValue = 'Not set';
                
                if (config.value) {
                    if (config.value === config.expected) {
                        status = 'success';
                        displayValue = config.label.includes('KEY') ? 
                            config.value.substring(0, 8) + '...' : 
                            config.value;
                    } else {
                        status = 'warning';
                        displayValue = `${config.value} (Expected: ${config.expected})`;
                    }
                }
                
                html += createConfigItem(config.label, displayValue, status);
            });
            
            // Check if import.meta.env is working
            html += '<h3>🔍 Environment Detection</h3>';
            html += createConfigItem(
                'import.meta.env available', 
                typeof import.meta.env !== 'undefined' ? 'Yes' : 'No',
                typeof import.meta.env !== 'undefined' ? 'success' : 'error'
            );
            
            html += createConfigItem(
                'NODE_ENV', 
                import.meta.env.NODE_ENV || 'Not set',
                import.meta.env.NODE_ENV === 'development' ? 'success' : 'warning'
            );
            
            html += createConfigItem(
                'MODE', 
                import.meta.env.MODE || 'Not set',
                import.meta.env.MODE === 'development' ? 'success' : 'warning'
            );
            
            resultsDiv.innerHTML = html;
        }

        async function testApiConnection() {
            const resultsDiv = document.getElementById('results');
            let html = '<h2>🌐 API Connection Test</h2>';
            
            const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
            const secretKey = import.meta.env.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
            const assistantId = import.meta.env.VITE_VAPI_ASSISTANT_ID || 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
            
            html += createConfigItem('Testing with Public Key', publicKey.substring(0, 8) + '...', 'info');
            html += createConfigItem('Testing with Secret Key', secretKey.substring(0, 8) + '...', 'info');
            html += createConfigItem('Testing Assistant ID', assistantId, 'info');
            
            try {
                // Test 1: List assistants with secret key
                html += '<h3>📋 Test 1: List Assistants (Secret Key)</h3>';
                
                const assistantsResponse = await fetch('https://api.vapi.ai/assistant', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${secretKey}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (assistantsResponse.ok) {
                    const assistants = await assistantsResponse.json();
                    html += createConfigItem(
                        'Assistants API', 
                        `✅ Success - Found ${assistants.length} assistants`, 
                        'success'
                    );
                } else {
                    const errorText = await assistantsResponse.text();
                    html += createConfigItem(
                        'Assistants API', 
                        `❌ Failed (${assistantsResponse.status}): ${errorText}`, 
                        'error'
                    );
                }
                
                // Test 2: Get specific assistant
                html += '<h3>📋 Test 2: Get Specific Assistant</h3>';
                
                const assistantResponse = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${secretKey}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (assistantResponse.ok) {
                    const assistant = await assistantResponse.json();
                    html += createConfigItem(
                        'Assistant Details', 
                        `✅ Found: ${assistant.name || 'Unnamed'}`, 
                        'success'
                    );
                    html += createConfigItem(
                        'Assistant Voice', 
                        `${assistant.voice?.provider || 'None'} - ${assistant.voice?.voiceId || 'None'}`, 
                        assistant.voice?.provider ? 'success' : 'warning'
                    );
                    html += createConfigItem(
                        'Assistant Model', 
                        `${assistant.model?.provider || 'None'} - ${assistant.model?.model || 'None'}`, 
                        assistant.model?.provider ? 'success' : 'warning'
                    );
                } else {
                    const errorText = await assistantResponse.text();
                    html += createConfigItem(
                        'Assistant Details', 
                        `❌ Failed (${assistantResponse.status}): ${errorText}`, 
                        'error'
                    );
                }
                
            } catch (error) {
                html += createConfigItem(
                    'API Connection', 
                    `❌ Error: ${error.message}`, 
                    'error'
                );
            }
            
            resultsDiv.innerHTML = html;
        }

        // Make functions available globally
        window.verifyConfig = verifyConfig;
        window.testApiConnection = testApiConnection;
        
        // Auto-run verification on load
        setTimeout(verifyConfig, 500);
    </script>
</body>
</html>
