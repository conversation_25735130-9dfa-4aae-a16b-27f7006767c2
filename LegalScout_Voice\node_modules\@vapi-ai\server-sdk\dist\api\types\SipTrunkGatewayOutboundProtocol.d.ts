/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the protocol to use for SIP signaling outbound calls. Default is udp.
 *
 * @default udp
 */
export type SipTrunkGatewayOutboundProtocol = "tls/srtp" | "tcp" | "tls" | "udp";
export declare const SipTrunkGatewayOutboundProtocol: {
    readonly TlsSrtp: "tls/srtp";
    readonly Tcp: "tcp";
    readonly Tls: "tls";
    readonly Udp: "udp";
};
