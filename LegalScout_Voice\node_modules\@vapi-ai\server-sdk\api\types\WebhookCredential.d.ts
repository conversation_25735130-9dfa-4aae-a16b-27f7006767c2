/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as Vapi from "../index";
export interface WebhookCredential {
    provider: "webhook";
    /** This is the authentication plan. Currently supports OAuth2 RFC 6749. */
    authenticationPlan: Vapi.OAuth2AuthenticationPlan;
    /** This is the unique identifier for the credential. */
    id: string;
    /** This is the unique identifier for the org that this credential belongs to. */
    orgId: string;
    /** This is the ISO 8601 date-time string of when the credential was created. */
    createdAt: string;
    /** This is the ISO 8601 date-time string of when the assistant was last updated. */
    updatedAt: string;
    /** This is the authentication session for the credential. Available for credentials that have an authentication plan. */
    authenticationSession: Vapi.Oauth2AuthenticationSession;
    /** This is the name of credential. This is just for your reference. */
    name?: string;
}
