/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface UpdateTrieveKnowledgeBaseDto {
    /** This is the name of the knowledge base. */
    name?: string;
    /**
     * This is the searching plan used when searching for relevant chunks from the vector store.
     *
     * You should configure this if you're running into these issues:
     * - Too much unnecessary context is being fed as knowledge base context.
     * - Not enough relevant context is being fed as knowledge base context.
     */
    searchPlan?: Vapi.TrieveKnowledgeBaseSearchPlan;
    /** This is the plan if you want us to create/import a new vector store using Trieve. */
    createPlan?: Vapi.TrieveKnowledgeBaseImport;
}
