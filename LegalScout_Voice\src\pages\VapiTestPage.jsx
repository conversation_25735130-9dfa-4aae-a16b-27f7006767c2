import React, { useState, useEffect } from 'react';
import useVapiCall from '../hooks/useVapiCall';
import VapiCall from '../components/VapiCall';
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';

const VapiTestPage = () => {
  const [testResults, setTestResults] = useState([]);
  const [isTestingConfig, setIsTestingConfig] = useState(false);
  const [configResults, setConfigResults] = useState(null);
  const [showVapiCall, setShowVapiCall] = useState(false);

  // Use the actual useVapiCall hook
  const {
    vapi,
    status,
    messageHistory,
    volumeLevel,
    assistantIsSpeaking,
    errorMessage,
    startCall,
    stopCall,
    dossierData
  } = useVapiCall({
    subdomain: 'test',
    assistantId: DEFAULT_ASSISTANT_ID,
    onEndCall: (data) => {
      addTestResult('Call ended with data:', 'info', data);
    }
  });

  const addTestResult = (message, type = 'info', data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    const result = {
      id: Date.now(),
      timestamp,
      message,
      type,
      data: data ? JSON.stringify(data, null, 2) : null
    };
    setTestResults(prev => [...prev, result]);
  };

  const testConfiguration = async () => {
    setIsTestingConfig(true);
    addTestResult('🔍 Testing LegalScout Voice configuration...', 'info');

    try {
      // Check environment variables
      const config = {
        vapiPublicKey: import.meta.env.VITE_VAPI_PUBLIC_KEY,
        vapiPrivateKey: import.meta.env.VITE_VAPI_PRIVATE_KEY,
        assistantId: import.meta.env.VITE_VAPI_ASSISTANT_ID,
        supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
        nodeEnv: import.meta.env.NODE_ENV
      };

      setConfigResults(config);

      // Test API connection
      if (config.vapiPrivateKey) {
        addTestResult('🔗 Testing Vapi API connection...', 'info');
        
        const response = await fetch('https://api.vapi.ai/assistant', {
          headers: {
            'Authorization': `Bearer ${config.vapiPrivateKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const assistants = await response.json();
          addTestResult(`✅ API connection successful - found ${assistants.length} assistants`, 'success');
          
          // Find our assistant
          const ourAssistant = assistants.find(a => a.id === config.assistantId);
          if (ourAssistant) {
            addTestResult(`✅ Found configured assistant: ${ourAssistant.name}`, 'success');
          } else {
            addTestResult(`⚠️ Configured assistant ID not found in available assistants`, 'warning');
          }
        } else {
          addTestResult(`❌ API connection failed: ${response.status} ${response.statusText}`, 'error');
        }
      }

      // Test SDK import
      addTestResult('📦 Testing Vapi SDK import...', 'info');
      try {
        const VapiModule = await import('@vapi-ai/web');
        const Vapi = VapiModule.default || VapiModule;
        
        if (typeof Vapi === 'function') {
          addTestResult('✅ Vapi SDK imported successfully', 'success');
          
          // Test instance creation
          if (config.vapiPublicKey) {
            const testInstance = new Vapi(config.vapiPublicKey);
            addTestResult('✅ Vapi instance created successfully', 'success');
          }
        } else {
          addTestResult(`❌ Vapi SDK import failed - got ${typeof Vapi}`, 'error');
        }
      } catch (error) {
        addTestResult(`❌ Vapi SDK import error: ${error.message}`, 'error');
      }

    } catch (error) {
      addTestResult(`❌ Configuration test failed: ${error.message}`, 'error');
    } finally {
      setIsTestingConfig(false);
    }
  };

  const testHookCall = async () => {
    addTestResult('📞 Testing useVapiCall hook...', 'info');
    
    try {
      await startCall();
      addTestResult('✅ Call started via hook', 'success');
    } catch (error) {
      addTestResult(`❌ Hook call failed: ${error.message}`, 'error');
    }
  };

  const testComponentCall = () => {
    addTestResult('🎯 Testing VapiCall component...', 'info');
    setShowVapiCall(true);
  };

  const clearResults = () => {
    setTestResults([]);
    setConfigResults(null);
  };

  // Monitor hook state changes
  useEffect(() => {
    if (status) {
      addTestResult(`📊 Hook status changed: ${status}`, 'info');
    }
  }, [status]);

  useEffect(() => {
    if (errorMessage) {
      addTestResult(`🚨 Hook error: ${errorMessage}`, 'error');
    }
  }, [errorMessage]);

  useEffect(() => {
    if (messageHistory.length > 0) {
      const lastMessage = messageHistory[messageHistory.length - 1];
      addTestResult(`💬 New message: ${lastMessage.role} - ${lastMessage.content}`, 'info');
    }
  }, [messageHistory]);

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '1200px', 
      margin: '0 auto',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        padding: '30px',
        borderRadius: '12px',
        marginBottom: '20px',
        textAlign: 'center'
      }}>
        <h1 style={{ margin: '0 0 10px 0', fontSize: '2.5em' }}>
          🧪 LegalScout Voice Test Lab
        </h1>
        <p style={{ margin: 0, opacity: 0.9 }}>
          Comprehensive testing for Vapi integration and call functionality
        </p>
      </div>

      {/* Test Controls */}
      <div style={{
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px',
        border: '1px solid #dee2e6'
      }}>
        <h3 style={{ margin: '0 0 15px 0' }}>🎮 Test Controls</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button
            onClick={testConfiguration}
            disabled={isTestingConfig}
            style={{
              background: '#007bff',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '6px',
              cursor: isTestingConfig ? 'not-allowed' : 'pointer',
              opacity: isTestingConfig ? 0.6 : 1
            }}
          >
            {isTestingConfig ? '⏳ Testing...' : '🔍 Test Configuration'}
          </button>
          
          <button
            onClick={testHookCall}
            style={{
              background: '#28a745',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            📞 Test Hook Call
          </button>
          
          <button
            onClick={testComponentCall}
            style={{
              background: '#17a2b8',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            🎯 Test Component
          </button>
          
          <button
            onClick={stopCall}
            style={{
              background: '#dc3545',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            🛑 Stop Call
          </button>
          
          <button
            onClick={clearResults}
            style={{
              background: '#6c757d',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            🧹 Clear Results
          </button>
        </div>
      </div>

      {/* Current Status */}
      <div style={{
        background: '#e9ecef',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px',
        border: '1px solid #dee2e6'
      }}>
        <h3 style={{ margin: '0 0 15px 0' }}>📊 Current Status</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <div style={{ background: 'white', padding: '15px', borderRadius: '6px', textAlign: 'center' }}>
            <div style={{ fontSize: '1.5em', fontWeight: 'bold', color: '#007bff' }}>
              {status || 'idle'}
            </div>
            <div style={{ color: '#6c757d', fontSize: '0.9em' }}>Call Status</div>
          </div>
          
          <div style={{ background: 'white', padding: '15px', borderRadius: '6px', textAlign: 'center' }}>
            <div style={{ fontSize: '1.5em', fontWeight: 'bold', color: '#28a745' }}>
              {messageHistory.length}
            </div>
            <div style={{ color: '#6c757d', fontSize: '0.9em' }}>Messages</div>
          </div>
          
          <div style={{ background: 'white', padding: '15px', borderRadius: '6px', textAlign: 'center' }}>
            <div style={{ fontSize: '1.5em', fontWeight: 'bold', color: '#ffc107' }}>
              {Math.round(volumeLevel * 100)}
            </div>
            <div style={{ color: '#6c757d', fontSize: '0.9em' }}>Volume Level</div>
          </div>
          
          <div style={{ background: 'white', padding: '15px', borderRadius: '6px', textAlign: 'center' }}>
            <div style={{ fontSize: '1.5em', fontWeight: 'bold', color: assistantIsSpeaking ? '#dc3545' : '#6c757d' }}>
              {assistantIsSpeaking ? '🗣️' : '🤐'}
            </div>
            <div style={{ color: '#6c757d', fontSize: '0.9em' }}>Speaking</div>
          </div>
        </div>
      </div>

      {/* Configuration Results */}
      {configResults && (
        <div style={{
          background: '#f8f9fa',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #dee2e6'
        }}>
          <h3 style={{ margin: '0 0 15px 0' }}>⚙️ Configuration</h3>
          <pre style={{
            background: '#343a40',
            color: '#f8f9fa',
            padding: '15px',
            borderRadius: '6px',
            overflow: 'auto',
            fontSize: '12px',
            margin: 0
          }}>
            {JSON.stringify(configResults, null, 2)}
          </pre>
        </div>
      )}

      {/* Test Results */}
      <div style={{
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px',
        border: '1px solid #dee2e6'
      }}>
        <h3 style={{ margin: '0 0 15px 0' }}>📝 Test Results</h3>
        <div style={{
          background: '#343a40',
          color: '#f8f9fa',
          padding: '15px',
          borderRadius: '6px',
          maxHeight: '400px',
          overflow: 'auto',
          fontSize: '13px',
          fontFamily: 'monospace'
        }}>
          {testResults.length === 0 ? (
            <div style={{ color: '#6c757d' }}>No test results yet. Run a test to see results here.</div>
          ) : (
            testResults.map(result => (
              <div key={result.id} style={{ marginBottom: '8px', paddingBottom: '8px', borderBottom: '1px solid #495057' }}>
                <div style={{
                  color: result.type === 'success' ? '#28a745' : 
                        result.type === 'error' ? '#dc3545' : 
                        result.type === 'warning' ? '#ffc107' : '#17a2b8'
                }}>
                  [{result.timestamp}] {result.message}
                </div>
                {result.data && (
                  <pre style={{ 
                    margin: '5px 0 0 20px', 
                    fontSize: '11px', 
                    color: '#adb5bd',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {result.data}
                  </pre>
                )}
              </div>
            ))
          )}
        </div>
      </div>

      {/* VapiCall Component Test */}
      {showVapiCall && (
        <div style={{
          background: '#f8f9fa',
          padding: '20px',
          borderRadius: '8px',
          marginBottom: '20px',
          border: '1px solid #dee2e6'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
            <h3 style={{ margin: 0 }}>🎯 VapiCall Component Test</h3>
            <button
              onClick={() => setShowVapiCall(false)}
              style={{
                background: '#dc3545',
                color: 'white',
                border: 'none',
                padding: '5px 10px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              ✕ Close
            </button>
          </div>
          
          <div style={{ border: '2px solid #dee2e6', borderRadius: '8px', overflow: 'hidden' }}>
            <VapiCall
              subdomain="test"
              assistantId={DEFAULT_ASSISTANT_ID}
              onEndCall={(data) => {
                addTestResult('VapiCall component ended', 'info', data);
                setShowVapiCall(false);
              }}
            />
          </div>
        </div>
      )}

      {/* Error Message Display */}
      {errorMessage && (
        <div style={{
          background: '#f8d7da',
          color: '#721c24',
          padding: '15px',
          borderRadius: '8px',
          border: '1px solid #f5c6cb',
          marginBottom: '20px'
        }}>
          <h4 style={{ margin: '0 0 10px 0' }}>🚨 Current Error</h4>
          <div style={{ fontFamily: 'monospace', fontSize: '13px' }}>
            {errorMessage}
          </div>
        </div>
      )}
    </div>
  );
};

export default VapiTestPage;
