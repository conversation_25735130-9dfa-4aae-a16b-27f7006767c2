/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the type of the message. "transcript" is sent as transcriber outputs partial or final transcript.
 */
export type ServerMessageTranscriptType = "transcript" | "transcript[transcriptType='final']";
export declare const ServerMessageTranscriptType: {
    readonly Transcript: "transcript";
    readonly TranscriptTranscriptTypeFinal: "transcript[transcriptType='final']";
};
