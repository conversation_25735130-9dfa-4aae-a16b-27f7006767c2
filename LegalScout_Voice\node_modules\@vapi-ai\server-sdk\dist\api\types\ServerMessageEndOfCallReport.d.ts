/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface ServerMessageEndOfCallReport {
    /**
     * This is the phone number associated with the call.
     *
     * This matches one of the following:
     * - `call.phoneNumber`,
     * - `call.phoneNumberId`.
     */
    phoneNumber?: Vapi.ServerMessageEndOfCallReportPhoneNumber;
    /** This is the type of the message. "end-of-call-report" is sent when the call ends and post-processing is complete. */
    type: "end-of-call-report";
    /** This is the reason the call ended. This can also be found at `call.endedReason` on GET /call/:id. */
    endedReason: Vapi.ServerMessageEndOfCallReportEndedReason;
    /** This is the cost of the call in USD. This can also be found at `call.cost` on GET /call/:id. */
    cost?: number;
    /** These are the costs of individual components of the call in USD. This can also be found at `call.costs` on GET /call/:id. */
    costs?: Vapi.ServerMessageEndOfCallReportCostsItem[];
    /** This is the timestamp of when the message was sent in milliseconds since Unix Epoch. */
    timestamp?: number;
    /** These are the artifacts from the call. This can also be found at `call.artifact` on GET /call/:id. */
    artifact: Vapi.Artifact;
    /**
     * This is the assistant that is currently active. This is provided for convenience.
     *
     * This matches one of the following:
     * - `call.assistant`,
     * - `call.assistantId`,
     * - `call.squad[n].assistant`,
     * - `call.squad[n].assistantId`,
     * - `call.squadId->[n].assistant`,
     * - `call.squadId->[n].assistantId`.
     */
    assistant?: Vapi.CreateAssistantDto;
    /**
     * This is the customer associated with the call.
     *
     * This matches one of the following:
     * - `call.customer`,
     * - `call.customerId`.
     */
    customer?: Vapi.CreateCustomerDto;
    /**
     * This is the call object.
     *
     * This matches what was returned in POST /call.
     *
     * Note: This might get stale during the call. To get the latest call object, especially after the call is ended, use GET /call/:id.
     */
    call?: Vapi.Call;
    /** This is the analysis of the call. This can also be found at `call.analysis` on GET /call/:id. */
    analysis: Vapi.Analysis;
    /** This is the ISO 8601 date-time string of when the call started. This can also be found at `call.startedAt` on GET /call/:id. */
    startedAt?: string;
    /** This is the ISO 8601 date-time string of when the call ended. This can also be found at `call.endedAt` on GET /call/:id. */
    endedAt?: string;
}
