<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LegalScout Voice Call Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-card h3 {
            margin: 0 0 15px 0;
            color: #fff;
            font-size: 1.3em;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
        }
        
        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .call-status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .status-idle {
            background: #6c757d;
            color: white;
        }
        
        .status-connecting {
            background: #ffc107;
            color: black;
            animation: pulse 1.5s infinite;
        }
        
        .status-connected {
            background: #28a745;
            color: white;
        }
        
        .status-error {
            background: #dc3545;
            color: white;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-timestamp {
            color: #adb5bd;
            margin-right: 8px;
        }
        
        .log-info { color: #17a2b8; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        
        .config-info {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .error-details {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #fff;
        }
        
        .metric-label {
            color: #adb5bd;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 LegalScout Voice Test</h1>
            <p>Direct integration test with your LegalScout Voice components</p>
        </div>
        
        <!-- Configuration Test -->
        <div class="test-card">
            <h3>📋 Configuration Check</h3>
            <button class="btn" onclick="checkConfig()">Check Environment</button>
            <button class="btn" onclick="testApiConnection()">Test API Connection</button>
            <div id="config-display" class="config-info" style="display: none;"></div>
        </div>
        
        <!-- Call Test -->
        <div class="test-card">
            <h3>📞 Voice Call Test</h3>
            <button class="btn btn-success" onclick="startTestCall()">Start Call</button>
            <button class="btn btn-danger" onclick="stopTestCall()">Stop Call</button>
            <button class="btn" onclick="testMicrophone()">Test Microphone</button>
            
            <div>
                Status: <span id="call-status" class="call-status status-idle">IDLE</span>
            </div>
            
            <div class="metrics">
                <div class="metric">
                    <div id="call-duration" class="metric-value">0s</div>
                    <div class="metric-label">Duration</div>
                </div>
                <div class="metric">
                    <div id="message-count" class="metric-value">0</div>
                    <div class="metric-label">Messages</div>
                </div>
                <div class="metric">
                    <div id="volume-level" class="metric-value">0</div>
                    <div class="metric-label">Volume</div>
                </div>
            </div>
        </div>
        
        <!-- Error Analysis -->
        <div class="test-card">
            <h3>🚨 Error Analysis</h3>
            <button class="btn" onclick="analyzeErrors()">Analyze Last Error</button>
            <button class="btn" onclick="exportLogs()">Export Logs</button>
            <button class="btn" onclick="clearLogs()">Clear Logs</button>
            <div id="error-analysis"></div>
        </div>
        
        <!-- Live Logs -->
        <div class="test-card">
            <h3>📝 Live Logs</h3>
            <div id="logs" class="log-container">
                <div class="log-entry">
                    <span class="log-timestamp">[Ready]</span>
                    <span class="log-info">LegalScout Voice test interface ready</span>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // Global state
        let vapiInstance = null;
        let callStartTime = null;
        let messageCount = 0;
        let lastError = null;
        let callDurationInterval = null;

        // Logging function
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logsContainer = document.getElementById('logs');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-${level}">${message}</span>
            `;
            logsContainer.appendChild(entry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }

        // Check configuration
        window.checkConfig = function() {
            log('🔍 Checking LegalScout Voice configuration...', 'info');
            
            const config = {
                vapiPublicKey: import.meta.env.VITE_VAPI_PUBLIC_KEY,
                vapiPrivateKey: import.meta.env.VITE_VAPI_PRIVATE_KEY,
                assistantId: import.meta.env.VITE_VAPI_ASSISTANT_ID,
                supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
                nodeEnv: import.meta.env.NODE_ENV || 'development'
            };
            
            let status = 'Configuration Status:\n';
            status += `• Environment: ${config.nodeEnv}\n`;
            status += `• Vapi Public Key: ${config.vapiPublicKey ? '✅ Present (' + config.vapiPublicKey.substring(0, 8) + '...)' : '❌ Missing'}\n`;
            status += `• Vapi Private Key: ${config.vapiPrivateKey ? '✅ Present (' + config.vapiPrivateKey.substring(0, 8) + '...)' : '❌ Missing'}\n`;
            status += `• Assistant ID: ${config.assistantId ? '✅ Present (' + config.assistantId.substring(0, 8) + '...)' : '❌ Missing'}\n`;
            status += `• Supabase URL: ${config.supabaseUrl ? '✅ Present' : '❌ Missing'}\n`;
            
            document.getElementById('config-display').textContent = status;
            document.getElementById('config-display').style.display = 'block';
            
            log('✅ Configuration check completed', 'success');
        };

        // Test API connection
        window.testApiConnection = async function() {
            log('🔗 Testing Vapi API connection...', 'info');
            
            try {
                const privateKey = import.meta.env.VITE_VAPI_PRIVATE_KEY;
                if (!privateKey) {
                    throw new Error('No private key available for API testing');
                }
                
                const response = await fetch('https://api.vapi.ai/assistant', {
                    headers: {
                        'Authorization': `Bearer ${privateKey}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const assistants = await response.json();
                    log(`✅ API connection successful - found ${assistants.length} assistants`, 'success');
                    
                    // Show assistant details
                    assistants.slice(0, 3).forEach((assistant, index) => {
                        log(`  ${index + 1}. ${assistant.name} (${assistant.id})`, 'info');
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                log(`❌ API connection failed: ${error.message}`, 'error');
                lastError = error;
            }
        };

        // Start test call
        window.startTestCall = async function() {
            log('📞 Starting LegalScout Voice test call...', 'info');
            
            try {
                // Update status
                updateCallStatus('connecting');
                
                // Get configuration
                const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
                const assistantId = import.meta.env.VITE_VAPI_ASSISTANT_ID;
                
                if (!publicKey) {
                    throw new Error('No public key configured');
                }
                
                if (!assistantId) {
                    throw new Error('No assistant ID configured');
                }
                
                log(`🔑 Using public key: ${publicKey.substring(0, 8)}...`, 'info');
                log(`🤖 Using assistant: ${assistantId.substring(0, 8)}...`, 'info');
                
                // Import and create Vapi instance
                log('📦 Loading Vapi SDK...', 'info');
                const VapiModule = await import('@vapi-ai/web');
                const Vapi = VapiModule.default || VapiModule;
                
                log('🏗️ Creating Vapi instance...', 'info');
                vapiInstance = new Vapi(publicKey);
                
                // Set up event listeners
                setupEventListeners();
                
                // Start the call
                log('🚀 Starting call...', 'info');
                const result = await vapiInstance.start(assistantId);
                
                log(`✅ Call started successfully: ${JSON.stringify(result)}`, 'success');
                
                // Start duration tracking
                callStartTime = Date.now();
                startDurationTracking();
                
            } catch (error) {
                log(`❌ Call failed: ${error.message}`, 'error');
                log(`📊 Error details: ${error.stack}`, 'error');
                updateCallStatus('error');
                lastError = error;
                showErrorAnalysis(error);
            }
        };

        // Setup event listeners
        function setupEventListeners() {
            if (!vapiInstance) return;
            
            vapiInstance.on('call-start', () => {
                log('🟢 Call started event received', 'success');
                updateCallStatus('connected');
            });
            
            vapiInstance.on('call-end', () => {
                log('🔴 Call ended event received', 'info');
                updateCallStatus('idle');
                stopDurationTracking();
            });
            
            vapiInstance.on('message', (message) => {
                messageCount++;
                log(`💬 Message: ${JSON.stringify(message)}`, 'info');
                document.getElementById('message-count').textContent = messageCount;
            });
            
            vapiInstance.on('error', (error) => {
                log(`🚨 Call error: ${error.message || JSON.stringify(error)}`, 'error');
                lastError = error;
                updateCallStatus('error');
            });
            
            vapiInstance.on('volume-level', (level) => {
                document.getElementById('volume-level').textContent = Math.round(level * 100);
            });
            
            vapiInstance.on('speech-start', () => {
                log('🗣️ Speech started', 'info');
            });
            
            vapiInstance.on('speech-end', () => {
                log('🤐 Speech ended', 'info');
            });
            
            vapiInstance.on('transcript', (transcript) => {
                log(`📝 Transcript: ${JSON.stringify(transcript)}`, 'info');
            });
        }

        // Update call status
        function updateCallStatus(status) {
            const statusElement = document.getElementById('call-status');
            statusElement.textContent = status.toUpperCase();
            statusElement.className = `call-status status-${status}`;
        }

        // Start duration tracking
        function startDurationTracking() {
            callDurationInterval = setInterval(() => {
                if (callStartTime) {
                    const duration = Math.floor((Date.now() - callStartTime) / 1000);
                    document.getElementById('call-duration').textContent = `${duration}s`;
                }
            }, 1000);
        }

        // Stop duration tracking
        function stopDurationTracking() {
            if (callDurationInterval) {
                clearInterval(callDurationInterval);
                callDurationInterval = null;
            }
            callStartTime = null;
        }

        // Stop test call
        window.stopTestCall = function() {
            if (!vapiInstance) {
                log('❌ No active call to stop', 'warning');
                return;
            }
            
            try {
                vapiInstance.stop();
                log('🛑 Call stop requested', 'info');
                updateCallStatus('idle');
                stopDurationTracking();
            } catch (error) {
                log(`❌ Error stopping call: ${error.message}`, 'error');
            }
        };

        // Test microphone
        window.testMicrophone = async function() {
            log('🎤 Testing microphone access...', 'info');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                log('✅ Microphone access granted', 'success');
                
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');
                
                log(`🎧 Found ${audioInputs.length} audio input devices`, 'info');
                audioInputs.forEach((device, index) => {
                    log(`  ${index + 1}. ${device.label || 'Unknown Device'}`, 'info');
                });
                
                stream.getTracks().forEach(track => track.stop());
                
            } catch (error) {
                log(`❌ Microphone test failed: ${error.message}`, 'error');
                lastError = error;
            }
        };

        // Show error analysis
        function showErrorAnalysis(error) {
            const analysisDiv = document.getElementById('error-analysis');
            
            const analysis = `
                <div class="error-details">
                    <h4>🚨 Error Analysis</h4>
                    <p><strong>Type:</strong> ${error.constructor.name}</p>
                    <p><strong>Message:</strong> ${error.message}</p>
                    <p><strong>Possible Causes:</strong></p>
                    <ul>
                        <li>Invalid or expired API key</li>
                        <li>Assistant ID doesn't exist or isn't accessible</li>
                        <li>Network connectivity issues</li>
                        <li>Microphone permissions denied</li>
                        <li>Vapi service temporarily unavailable</li>
                    </ul>
                    <p><strong>Next Steps:</strong></p>
                    <ul>
                        <li>Check API key in Vapi dashboard</li>
                        <li>Verify assistant configuration</li>
                        <li>Test with a different browser</li>
                        <li>Check browser console for additional errors</li>
                    </ul>
                </div>
            `;
            
            analysisDiv.innerHTML = analysis;
        }

        // Analyze errors
        window.analyzeErrors = function() {
            if (!lastError) {
                log('ℹ️ No recent errors to analyze', 'info');
                return;
            }
            
            log('🔍 Analyzing last error...', 'info');
            showErrorAnalysis(lastError);
        };

        // Export logs
        window.exportLogs = function() {
            const logs = Array.from(document.querySelectorAll('.log-entry')).map(entry => entry.textContent);
            const data = {
                timestamp: new Date().toISOString(),
                logs: logs,
                lastError: lastError ? {
                    message: lastError.message,
                    stack: lastError.stack,
                    type: lastError.constructor.name
                } : null,
                config: {
                    hasPublicKey: !!import.meta.env.VITE_VAPI_PUBLIC_KEY,
                    hasPrivateKey: !!import.meta.env.VITE_VAPI_PRIVATE_KEY,
                    hasAssistantId: !!import.meta.env.VITE_VAPI_ASSISTANT_ID
                }
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `legalscout-voice-test-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📁 Test logs exported', 'success');
        };

        // Clear logs
        window.clearLogs = function() {
            document.getElementById('logs').innerHTML = `
                <div class="log-entry">
                    <span class="log-timestamp">[Cleared]</span>
                    <span class="log-info">Logs cleared - ready for new tests</span>
                </div>
            `;
            messageCount = 0;
            document.getElementById('message-count').textContent = '0';
            log('🧹 Logs cleared', 'info');
        };

        // Initialize
        log('🚀 LegalScout Voice test interface initialized', 'success');
        log('💡 Click "Check Environment" to verify configuration', 'info');
        
    </script>
</body>
</html>
