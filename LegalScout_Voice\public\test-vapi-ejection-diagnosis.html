<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Ejection Diagnosis</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: 600;
            border-left: 5px solid;
        }
        .status.success { 
            background-color: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .status.warning { 
            background-color: #fff3cd; 
            color: #856404; 
            border-left-color: #ffc107;
        }
        .status.error { 
            background-color: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        .status.info { 
            background-color: #d1ecf1; 
            color: #0c5460; 
            border-left-color: #17a2b8;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }
        button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 8px 5px;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        button:disabled { 
            background: #6c757d; 
            cursor: not-allowed; 
            transform: none;
            box-shadow: none;
        }
        .log {
            background-color: #2d3748;
            color: #e2e8f0;
            border: 1px solid #4a5568;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px 0;
        }
        .log-entry.error { color: #fc8181; }
        .log-entry.success { color: #68d391; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }
        .config-display {
            background-color: #f1f3f4;
            border: 1px solid #dadce0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Vapi Ejection Diagnosis Tool</h1>
            <p>Comprehensive analysis of "Meeting ended due to ejection" errors</p>
        </div>
        
        <div id="status" class="status info">
            Ready to diagnose Vapi ejection issues...
        </div>

        <div class="grid">
            <div>
                <button onclick="runFullDiagnosis()">
                    🚀 Run Full Diagnosis
                </button>
                
                <button onclick="testApiKeys()">
                    🔑 Test API Keys
                </button>
                
                <button onclick="testAssistantConfig()">
                    🤖 Test Assistant Config
                </button>
            </div>
            <div>
                <button onclick="testVapiConnection()">
                    🌐 Test Vapi Connection
                </button>
                
                <button onclick="simulateCall()">
                    📞 Simulate Call Start
                </button>
                
                <button onclick="clearLogs()">
                    🗑️ Clear Logs
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 Diagnosis Results</h3>
            <div id="diagnosis-results"></div>
        </div>

        <div class="test-section">
            <h3>📝 Detailed Logs</h3>
            <div id="logs" class="log"></div>
        </div>
    </div>

    <script type="module">
        let diagnosticResults = {};
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function updateResults() {
            const resultsDiv = document.getElementById('diagnosis-results');
            let html = '<div class="config-display">';
            
            for (const [test, result] of Object.entries(diagnosticResults)) {
                const status = result.success ? '✅' : '❌';
                const color = result.success ? '#28a745' : '#dc3545';
                html += `<div style="color: ${color}; margin: 8px 0;">
                    ${status} <strong>${test}</strong>: ${result.message}
                </div>`;
                
                if (result.details) {
                    html += `<div style="margin-left: 20px; color: #6c757d; font-size: 11px;">
                        ${result.details}
                    </div>`;
                }
            }
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        // Test API Keys
        async function testApiKeys() {
            log('🔑 Testing API Keys...', 'info');
            updateStatus('Testing API Keys...', 'info');
            
            try {
                // Get environment variables from .env.development
                const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
                const secretKey = import.meta.env.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
                
                log(`Public Key: ${publicKey.substring(0, 8)}...`, 'info');
                log(`Secret Key: ${secretKey.substring(0, 8)}...`, 'info');
                
                // Test public key format
                const publicKeyValid = publicKey && publicKey.length > 20 && publicKey.includes('-');
                const secretKeyValid = secretKey && secretKey.length > 20 && secretKey.includes('-');
                
                diagnosticResults['API Key Format'] = {
                    success: publicKeyValid && secretKeyValid,
                    message: publicKeyValid && secretKeyValid ? 'Valid format' : 'Invalid format',
                    details: `Public: ${publicKeyValid ? 'Valid' : 'Invalid'}, Secret: ${secretKeyValid ? 'Valid' : 'Invalid'}`
                };
                
                // Test API key by making a simple request
                try {
                    const response = await fetch('https://api.vapi.ai/assistant', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${secretKey}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    log(`API Response Status: ${response.status}`, response.ok ? 'success' : 'error');
                    
                    if (response.ok) {
                        const data = await response.json();
                        log(`Found ${data.length || 0} assistants`, 'success');
                        
                        diagnosticResults['API Key Authentication'] = {
                            success: true,
                            message: `Valid - Found ${data.length || 0} assistants`,
                            details: `Status: ${response.status}`
                        };
                    } else {
                        const errorText = await response.text();
                        log(`API Error: ${errorText}`, 'error');
                        
                        diagnosticResults['API Key Authentication'] = {
                            success: false,
                            message: `Failed - Status ${response.status}`,
                            details: errorText
                        };
                    }
                } catch (apiError) {
                    log(`API Request Failed: ${apiError.message}`, 'error');
                    
                    diagnosticResults['API Key Authentication'] = {
                        success: false,
                        message: 'Request failed',
                        details: apiError.message
                    };
                }
                
                updateResults();
                
            } catch (error) {
                log(`API Key Test Failed: ${error.message}`, 'error');
                updateStatus('API Key test failed', 'error');
            }
        }

        // Test Assistant Configuration
        async function testAssistantConfig() {
            log('🤖 Testing Assistant Configuration...', 'info');
            updateStatus('Testing Assistant Configuration...', 'info');

            try {
                const assistantId = import.meta.env.VITE_VAPI_ASSISTANT_ID || 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
                const secretKey = import.meta.env.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

                log(`Testing Assistant ID: ${assistantId}`, 'info');

                // Test assistant exists
                try {
                    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${secretKey}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    log(`Assistant API Response: ${response.status}`, response.ok ? 'success' : 'error');

                    if (response.ok) {
                        const assistant = await response.json();
                        log(`Assistant found: ${assistant.name || 'Unnamed'}`, 'success');
                        log(`Assistant model: ${assistant.model?.provider || 'Unknown'} ${assistant.model?.model || ''}`, 'info');
                        log(`Assistant voice: ${assistant.voice?.provider || 'Unknown'} ${assistant.voice?.voiceId || ''}`, 'info');

                        // Check for common ejection causes
                        const hasValidModel = assistant.model && assistant.model.provider;
                        const hasValidVoice = assistant.voice && assistant.voice.provider;
                        const hasFirstMessage = assistant.firstMessage || assistant.firstMessageMode;

                        diagnosticResults['Assistant Exists'] = {
                            success: true,
                            message: `Found: ${assistant.name || 'Unnamed'}`,
                            details: `Model: ${assistant.model?.provider || 'None'}, Voice: ${assistant.voice?.provider || 'None'}`
                        };

                        diagnosticResults['Assistant Configuration'] = {
                            success: hasValidModel && hasValidVoice,
                            message: hasValidModel && hasValidVoice ? 'Valid configuration' : 'Missing required config',
                            details: `Model: ${hasValidModel ? 'OK' : 'Missing'}, Voice: ${hasValidVoice ? 'OK' : 'Missing'}, FirstMessage: ${hasFirstMessage ? 'OK' : 'Missing'}`
                        };

                        // Log full assistant config for debugging
                        log('Full Assistant Config:', 'info');
                        log(JSON.stringify(assistant, null, 2), 'info');

                    } else {
                        const errorText = await response.text();
                        log(`Assistant not found: ${errorText}`, 'error');

                        diagnosticResults['Assistant Exists'] = {
                            success: false,
                            message: `Not found - Status ${response.status}`,
                            details: errorText
                        };
                    }
                } catch (assistantError) {
                    log(`Assistant API Error: ${assistantError.message}`, 'error');

                    diagnosticResults['Assistant Exists'] = {
                        success: false,
                        message: 'API request failed',
                        details: assistantError.message
                    };
                }

                updateResults();

            } catch (error) {
                log(`Assistant Config Test Failed: ${error.message}`, 'error');
                updateStatus('Assistant config test failed', 'error');
            }
        }

        // Test Vapi Connection
        async function testVapiConnection() {
            log('🌐 Testing Vapi Connection...', 'info');
            updateStatus('Testing Vapi Connection...', 'info');

            try {
                // Test if we can load the Vapi SDK
                log('Loading Vapi SDK...', 'info');

                const { loadVapiSDK, createVapiInstance } = await import('/src/utils/vapiLoader.js');

                const VapiClass = await loadVapiSDK();
                log(`Vapi SDK loaded: ${typeof VapiClass}`, 'success');

                diagnosticResults['Vapi SDK Loading'] = {
                    success: typeof VapiClass === 'function',
                    message: typeof VapiClass === 'function' ? 'Successfully loaded' : 'Failed to load',
                    details: `Type: ${typeof VapiClass}`
                };

                // Test creating instance
                const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
                const vapiInstance = await createVapiInstance(publicKey);

                log(`Vapi instance created: ${!!vapiInstance}`, vapiInstance ? 'success' : 'error');
                log(`Instance has start method: ${typeof vapiInstance?.start}`, typeof vapiInstance?.start === 'function' ? 'success' : 'error');

                diagnosticResults['Vapi Instance Creation'] = {
                    success: !!vapiInstance && typeof vapiInstance.start === 'function',
                    message: !!vapiInstance && typeof vapiInstance.start === 'function' ? 'Instance created successfully' : 'Failed to create instance',
                    details: `Has start method: ${typeof vapiInstance?.start}`
                };

                updateResults();

            } catch (error) {
                log(`Vapi Connection Test Failed: ${error.message}`, 'error');
                log(`Error stack: ${error.stack}`, 'error');

                diagnosticResults['Vapi Connection'] = {
                    success: false,
                    message: 'Connection failed',
                    details: error.message
                };

                updateResults();
                updateStatus('Vapi connection test failed', 'error');
            }
        }

        // Simulate Call Start
        async function simulateCall() {
            log('📞 Simulating Call Start...', 'info');
            updateStatus('Simulating Call Start...', 'info');

            try {
                const { loadVapiSDK, createVapiInstance } = await import('/src/utils/vapiLoader.js');

                const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
                const assistantId = import.meta.env.VITE_VAPI_ASSISTANT_ID || 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

                log('Creating Vapi instance for call simulation...', 'info');
                const vapiInstance = await createVapiInstance(publicKey);

                // Set up event listeners to capture ejection
                const eventResults = {};

                const setupListener = (eventName) => {
                    if (vapiInstance && typeof vapiInstance.on === 'function') {
                        vapiInstance.on(eventName, (data) => {
                            log(`Event [${eventName}]: ${JSON.stringify(data)}`, eventName.includes('error') || eventName.includes('end') ? 'error' : 'info');
                            eventResults[eventName] = data;
                        });
                    }
                };

                // Set up all possible event listeners
                const events = [
                    'call-start', 'call-started', 'call-end', 'call-ended',
                    'error', 'speech-start', 'speech-end', 'message',
                    'transcript', 'transcription', 'volume-level'
                ];

                events.forEach(setupListener);

                log('Event listeners set up, attempting to start call...', 'info');

                try {
                    // Try to start the call
                    const callResult = await vapiInstance.start(assistantId);
                    log(`Call start result: ${JSON.stringify(callResult)}`, 'success');

                    diagnosticResults['Call Start'] = {
                        success: true,
                        message: 'Call started successfully',
                        details: JSON.stringify(callResult)
                    };

                    // Wait a bit to see if ejection occurs
                    setTimeout(() => {
                        log('Checking for ejection events...', 'info');

                        const hasEjection = Object.keys(eventResults).some(event =>
                            event.includes('end') || event.includes('error')
                        );

                        if (hasEjection) {
                            log('Ejection detected! Event details:', 'error');
                            Object.entries(eventResults).forEach(([event, data]) => {
                                log(`${event}: ${JSON.stringify(data)}`, 'error');
                            });
                        } else {
                            log('No ejection detected in first 5 seconds', 'success');
                        }

                        // Stop the call
                        if (vapiInstance && typeof vapiInstance.stop === 'function') {
                            vapiInstance.stop();
                            log('Call stopped', 'info');
                        }
                    }, 5000);

                } catch (callError) {
                    log(`Call start failed: ${callError.message}`, 'error');
                    log(`Call error stack: ${callError.stack}`, 'error');

                    diagnosticResults['Call Start'] = {
                        success: false,
                        message: 'Call start failed',
                        details: callError.message
                    };
                }

                updateResults();

            } catch (error) {
                log(`Call Simulation Failed: ${error.message}`, 'error');
                log(`Simulation error stack: ${error.stack}`, 'error');

                diagnosticResults['Call Simulation'] = {
                    success: false,
                    message: 'Simulation failed',
                    details: error.message
                };

                updateResults();
                updateStatus('Call simulation failed', 'error');
            }
        }

        // Run Full Diagnosis
        async function runFullDiagnosis() {
            log('🚀 Starting Full Diagnosis...', 'info');
            updateStatus('Running full diagnosis...', 'info');

            diagnosticResults = {};

            try {
                await testApiKeys();
                await new Promise(resolve => setTimeout(resolve, 1000));

                await testAssistantConfig();
                await new Promise(resolve => setTimeout(resolve, 1000));

                await testVapiConnection();
                await new Promise(resolve => setTimeout(resolve, 1000));

                await simulateCall();

                log('✅ Full diagnosis completed', 'success');
                updateStatus('Full diagnosis completed', 'success');

            } catch (error) {
                log(`Full Diagnosis Failed: ${error.message}`, 'error');
                updateStatus('Full diagnosis failed', 'error');
            }
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
            document.getElementById('diagnosis-results').innerHTML = '';
            diagnosticResults = {};
            updateStatus('Logs cleared', 'info');
        }

        // Make functions available globally
        window.runFullDiagnosis = runFullDiagnosis;
        window.testApiKeys = testApiKeys;
        window.testAssistantConfig = testAssistantConfig;
        window.testVapiConnection = testVapiConnection;
        window.simulateCall = simulateCall;
        window.clearLogs = clearLogs;
        window.updateStatus = updateStatus;
        window.updateResults = updateResults;
        window.log = log;
        window.diagnosticResults = diagnosticResults;

        // Auto-run basic tests on load
        setTimeout(() => {
            log('🔍 Auto-running basic diagnosis...', 'info');
            testApiKeys();
        }, 1000);
    </script>
</body>
</html>
