<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Vapi SDK Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .test-result.pass {
            background-color: #d4edda;
            border-left-color: #28a745;
        }
        .test-result.fail {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Vapi SDK Fix Test</h1>
        <p>This test verifies that the Vapi SDK loading fix is working correctly.</p>
        
        <div id="status" class="status info">
            Ready to test...
        </div>

        <button id="testFix" onclick="testVapiFix()">
            🚀 Test Vapi SDK Fix
        </button>
        
        <button id="testImport" onclick="testDirectImport()">
            📦 Test Direct Import
        </button>

        <div id="results"></div>
        <div id="logs" class="log"></div>
    </div>

    <script type="module">
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function addTestResult(testName, passed, details) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = `
                <strong>${testName}</strong>: ${passed ? '✅ PASS' : '❌ FAIL'}
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // Test the fixed Vapi loader
        async function testVapiFix() {
            updateStatus('Testing Vapi SDK fix...', 'info');
            document.getElementById('logs').innerHTML = '';
            document.getElementById('results').innerHTML = '';
            
            log('🚀 Starting Vapi SDK fix test...');
            
            try {
                // Test 1: Direct import of @vapi-ai/web
                log('📋 Test 1: Testing direct import of @vapi-ai/web');
                try {
                    const VapiModule = await import('@vapi-ai/web');
                    log('✅ Direct import successful');
                    log(`Module structure: ${Object.keys(VapiModule).join(', ')}`);
                    
                    // Extract Vapi class using our fixed logic
                    let VapiClass = VapiModule.default || VapiModule.Vapi || VapiModule;
                    
                    // Additional check for CommonJS structure
                    if (!VapiClass && VapiModule && typeof VapiModule === 'object') {
                        VapiClass = VapiModule.default?.default || VapiModule;
                    }
                    
                    if (typeof VapiClass === 'function') {
                        log('✅ Vapi class found and is a function');
                        addTestResult('Direct Import', true, 'Successfully imported @vapi-ai/web package');
                        
                        // Test instantiation
                        try {
                            const testInstance = new VapiClass('test-key');
                            if (testInstance && typeof testInstance.start === 'function') {
                                log('✅ Vapi instance created successfully');
                                addTestResult('Instance Creation', true, 'Vapi instance has start method');
                                updateStatus('✅ Vapi SDK fix working correctly!', 'success');
                            } else {
                                log('❌ Vapi instance missing start method');
                                addTestResult('Instance Creation', false, 'Missing start method');
                                updateStatus('❌ Vapi instance validation failed', 'error');
                            }
                        } catch (error) {
                            log(`❌ Failed to create Vapi instance: ${error.message}`);
                            addTestResult('Instance Creation', false, error.message);
                            updateStatus(`❌ Instance creation failed: ${error.message}`, 'error');
                        }
                        
                    } else {
                        log(`❌ Vapi class is not a function, got: ${typeof VapiClass}`);
                        addTestResult('Direct Import', false, `Expected function, got ${typeof VapiClass}`);
                        updateStatus(`❌ Invalid Vapi class type: ${typeof VapiClass}`, 'error');
                    }
                    
                } catch (error) {
                    log(`❌ Direct import failed: ${error.message}`);
                    addTestResult('Direct Import', false, error.message);
                    updateStatus(`❌ Import failed: ${error.message}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
                updateStatus(`❌ Test failed: ${error.message}`, 'error');
            }
        }

        // Test direct import without our loader
        async function testDirectImport() {
            updateStatus('Testing direct import...', 'info');
            document.getElementById('logs').innerHTML = '';
            document.getElementById('results').innerHTML = '';
            
            log('📦 Testing direct import of @vapi-ai/web...');
            
            try {
                const VapiModule = await import('@vapi-ai/web');
                log('✅ Import successful');
                log(`Module keys: ${Object.keys(VapiModule).join(', ')}`);
                log(`Module.default type: ${typeof VapiModule.default}`);
                log(`Module type: ${typeof VapiModule}`);
                
                addTestResult('Module Import', true, `Keys: ${Object.keys(VapiModule).join(', ')}`);
                updateStatus('✅ Direct import successful', 'success');
                
            } catch (error) {
                log(`❌ Import failed: ${error.message}`, 'error');
                addTestResult('Module Import', false, error.message);
                updateStatus(`❌ Import failed: ${error.message}`, 'error');
            }
        }

        // Make functions available globally
        window.testVapiFix = testVapiFix;
        window.testDirectImport = testDirectImport;
    </script>
</body>
</html>
