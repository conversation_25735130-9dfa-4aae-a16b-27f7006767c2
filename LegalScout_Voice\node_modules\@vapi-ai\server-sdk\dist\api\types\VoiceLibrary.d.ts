/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as Vapi from "../index";
export interface VoiceLibrary {
    /** This is the voice provider that will be used. */
    provider?: Record<string, unknown>;
    /** The ID of the voice provided by the provider. */
    providerId?: string;
    /** The unique slug of the voice. */
    slug?: string;
    /** The name of the voice. */
    name?: string;
    /** The language of the voice. */
    language?: string;
    /** The language code of the voice. */
    languageCode?: string;
    /** The model of the voice. */
    model?: string;
    /** The supported models of the voice. */
    supportedModels?: string;
    /** The gender of the voice. */
    gender?: Vapi.VoiceLibraryGender;
    /** The accent of the voice. */
    accent?: string;
    /** The preview URL of the voice. */
    previewUrl?: string;
    /** The description of the voice. */
    description?: string;
    /** The credential ID of the voice. */
    credentialId?: string;
    /** The unique identifier for the voice library. */
    id: string;
    /** The unique identifier for the organization that this voice library belongs to. */
    orgId: string;
    /** The Public voice is shared accross all the organizations. */
    isPublic: boolean;
    /** The deletion status of the voice. */
    isDeleted: boolean;
    /** The ISO 8601 date-time string of when the voice library was created. */
    createdAt: string;
    /** The ISO 8601 date-time string of when the voice library was last updated. */
    updatedAt: string;
}
