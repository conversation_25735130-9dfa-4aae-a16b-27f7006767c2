#!/usr/bin/env node

/**
 * Test both keys to determine which one is the real Vapi API key
 */

import https from 'https';

// The two keys from your .env file
const KEY_1 = '310f0d43-27c2-47a5-a76d-e55171d024f7'; // Currently used as API key
const KEY_2 = '6734febc-fc65-4669-93b0-929b31ff6564'; // Secret key from env
const DEFAULT_ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

console.log('🔍 Testing both keys to determine which is the real Vapi API key...\n');

// Helper function to make HTTP requests
function makeRequest(url, headers) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: 'GET',
      headers: headers
    }, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

// Test a key against Vapi API
async function testKey(keyName, keyValue, description) {
  console.log(`\n📋 Testing ${keyName}: ${description}`);
  console.log('=' .repeat(60));

  const headers = {
    'Authorization': `Bearer ${keyValue}`,
    'Content-Type': 'application/json',
    'User-Agent': 'LegalScout-Diagnostic/1.0'
  };

  let isValidApiKey = false;

  // Test 1: Account endpoint
  try {
    console.log('🔍 Test 1: Account access...');
    const accountResponse = await makeRequest('https://api.vapi.ai/account', headers);
    
    if (accountResponse.status === 200) {
      console.log('✅ SUCCESS: Account endpoint accessible');
      try {
        const accountData = JSON.parse(accountResponse.body);
        console.log(`   Account info: ${accountData.email || accountData.id || 'Found account data'}`);
      } catch (e) {
        console.log('   Account data received (could not parse JSON)');
      }
      isValidApiKey = true;
    } else if (accountResponse.status === 401) {
      console.log('❌ FAILED: Unauthorized (401) - Not a valid API key');
    } else if (accountResponse.status === 404) {
      console.log('⚠️  WARNING: Not found (404) - Endpoint may not exist');
    } else {
      console.log(`❌ FAILED: HTTP ${accountResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
  }

  // Test 2: Assistants endpoint
  try {
    console.log('🔍 Test 2: Assistants list...');
    const assistantsResponse = await makeRequest('https://api.vapi.ai/assistant', headers);
    
    if (assistantsResponse.status === 200) {
      console.log('✅ SUCCESS: Assistants endpoint accessible');
      try {
        const assistants = JSON.parse(assistantsResponse.body);
        const count = Array.isArray(assistants) ? assistants.length : 'unknown';
        console.log(`   Found ${count} assistants`);
      } catch (e) {
        console.log('   Assistants data received (could not parse JSON)');
      }
      isValidApiKey = true;
    } else if (assistantsResponse.status === 401) {
      console.log('❌ FAILED: Unauthorized (401) - Not a valid API key');
    } else if (assistantsResponse.status === 404) {
      console.log('⚠️  WARNING: Not found (404) - Endpoint may not exist');
    } else {
      console.log(`❌ FAILED: HTTP ${assistantsResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
  }

  // Test 3: Specific assistant endpoint
  try {
    console.log('🔍 Test 3: Specific assistant access...');
    const assistantResponse = await makeRequest(`https://api.vapi.ai/assistant/${DEFAULT_ASSISTANT_ID}`, headers);
    
    if (assistantResponse.status === 200) {
      console.log('✅ SUCCESS: Specific assistant accessible');
      try {
        const assistant = JSON.parse(assistantResponse.body);
        console.log(`   Assistant: ${assistant.name || assistant.id || 'Found assistant'}`);
      } catch (e) {
        console.log('   Assistant data received (could not parse JSON)');
      }
      isValidApiKey = true;
    } else if (assistantResponse.status === 401) {
      console.log('❌ FAILED: Unauthorized (401) - Not a valid API key');
    } else if (assistantResponse.status === 404) {
      console.log('⚠️  WARNING: Assistant not found (404) - May not exist or wrong ID');
    } else {
      console.log(`❌ FAILED: HTTP ${assistantResponse.status}`);
    }
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
  }

  return isValidApiKey;
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Vapi API Key Tests');
  console.log('Testing which key is the real API key vs assistant ID\n');

  const results = [];

  // Test Key 1
  const key1Valid = await testKey(
    'Key 1 (Currently used as API key)', 
    KEY_1, 
    '310f0d43-27c2-47a5-a76d-e55171d024f7'
  );
  results.push({ name: 'Key 1', value: KEY_1, valid: key1Valid });

  // Test Key 2  
  const key2Valid = await testKey(
    'Key 2 (Secret key from env)', 
    KEY_2, 
    '6734febc-fc65-4669-93b0-929b31ff6564'
  );
  results.push({ name: 'Key 2', value: KEY_2, valid: key2Valid });

  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('🎯 FINAL RESULTS');
  console.log('='.repeat(60));

  const validKeys = results.filter(r => r.valid);
  
  if (validKeys.length === 0) {
    console.log('❌ CONCLUSION: Neither key works as an API key');
    console.log('💡 RECOMMENDATION: Generate a new API key from your Vapi dashboard');
    console.log('   Go to: https://dashboard.vapi.ai → Settings → API Keys');
  } else if (validKeys.length === 1) {
    const workingKey = validKeys[0];
    console.log(`🎉 CONCLUSION: ${workingKey.name} is the REAL API KEY!`);
    console.log(`   Working key: ${workingKey.value}`);
    
    if (workingKey.value === KEY_2) {
      console.log('\n💡 RECOMMENDATIONS:');
      console.log('   1. Update your .env file:');
      console.log(`      VITE_VAPI_PUBLIC_KEY=${KEY_2}`);
      console.log('   2. The current key (310f0d43...) appears to be an assistant ID, not an API key');
      console.log('   3. Restart your development server after updating .env');
    }
  } else {
    console.log('🤔 CONCLUSION: Both keys work as API keys (unusual)');
    console.log('   This might indicate both are valid API keys for your account');
  }

  console.log('\n✅ Test completed!');
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test failed:', error);
  process.exit(1);
});
