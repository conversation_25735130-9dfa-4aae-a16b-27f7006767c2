/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The voices provided by Vapi
 */
export type VapiVoiceVoiceId = "<PERSON>" | "<PERSON>oh<PERSON>" | "<PERSON>" | "<PERSON>" | "<PERSON><PERSON>" | "<PERSON>eh<PERSON>" | "<PERSON>" | "<PERSON>" | "<PERSON>" | "<PERSON>";
export declare const VapiVoiceVoiceId: {
    readonly <PERSON>: "<PERSON>";
    readonly <PERSON>: "<PERSON><PERSON><PERSON>";
    readonly <PERSON>: "<PERSON>";
    readonly <PERSON>: "<PERSON>";
    readonly <PERSON>: "<PERSON><PERSON>";
    readonly <PERSON><PERSON><PERSON>: "<PERSON>eh<PERSON>";
    readonly <PERSON>: "<PERSON>";
    readonly <PERSON>: "<PERSON>";
    readonly <PERSON>: "<PERSON>";
    readonly <PERSON>: "<PERSON>";
};
