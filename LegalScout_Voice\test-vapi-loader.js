#!/usr/bin/env node

/**
 * Test script to verify Vapi SDK loading fix
 * Run with: node test-vapi-loader.js
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🔧 Testing Vapi SDK Loading Fix...\n');

async function testVapiImport() {
    console.log('📋 Test 1: Direct import of @vapi-ai/web');
    
    try {
        // This simulates what happens in the browser
        const modulePath = join(__dirname, 'node_modules', '@vapi-ai', 'web', 'dist', 'vapi.js');
        console.log(`Attempting to import from: ${modulePath}`);
        
        // In Node.js, we need to use require for CommonJS modules
        const { createRequire } = await import('module');
        const require = createRequire(import.meta.url);
        
        const VapiModule = require('@vapi-ai/web');
        console.log('✅ Import successful');
        console.log(`Module type: ${typeof VapiModule}`);
        console.log(`Module keys: ${Object.keys(VapiModule).join(', ')}`);
        
        // Test our extraction logic
        let VapiClass = VapiModule.default || VapiModule.Vapi || VapiModule;
        
        // Additional check for CommonJS structure
        if (!VapiClass && VapiModule && typeof VapiModule === 'object') {
            VapiClass = VapiModule.default?.default || VapiModule;
        }
        
        console.log(`Extracted Vapi class type: ${typeof VapiClass}`);
        
        if (typeof VapiClass === 'function') {
            console.log('✅ Vapi class is a function');
            
            // Test instantiation
            try {
                const testInstance = new VapiClass('test-key');
                console.log('✅ Vapi instance created');
                
                if (testInstance && typeof testInstance.start === 'function') {
                    console.log('✅ Vapi instance has start method');
                    console.log('✅ All tests passed!');
                } else {
                    console.log('❌ Vapi instance missing start method');
                }
                
            } catch (error) {
                console.log(`❌ Failed to create Vapi instance: ${error.message}`);
            }
            
        } else {
            console.log(`❌ Vapi class is not a function, got: ${typeof VapiClass}`);
        }
        
    } catch (error) {
        console.log(`❌ Import failed: ${error.message}`);
        console.log('Stack trace:', error.stack);
    }
}

async function testPackageStructure() {
    console.log('\n📋 Test 2: Package structure analysis');
    
    try {
        const { createRequire } = await import('module');
        const require = createRequire(import.meta.url);
        
        // Check package.json
        const packageJson = require('@vapi-ai/web/package.json');
        console.log(`Package version: ${packageJson.version}`);
        console.log(`Main entry: ${packageJson.main}`);
        console.log(`Types entry: ${packageJson.types}`);
        
        // Check if the main file exists
        const fs = await import('fs');
        const path = await import('path');
        
        const packageDir = path.dirname(require.resolve('@vapi-ai/web/package.json'));
        const mainFile = path.join(packageDir, packageJson.main);
        
        if (fs.existsSync(mainFile)) {
            console.log(`✅ Main file exists: ${mainFile}`);
        } else {
            console.log(`❌ Main file missing: ${mainFile}`);
        }
        
    } catch (error) {
        console.log(`❌ Package structure test failed: ${error.message}`);
    }
}

async function main() {
    await testVapiImport();
    await testPackageStructure();
    
    console.log('\n🎯 Test Summary:');
    console.log('- The Vapi SDK should now load correctly in the browser');
    console.log('- The CommonJS export structure is properly handled');
    console.log('- Vite configuration includes @vapi-ai/web in optimization');
    console.log('\n💡 Next steps:');
    console.log('1. Run the development server: npm run dev');
    console.log('2. Open the test page: http://localhost:5174/test-vapi-sdk-fix.html');
    console.log('3. Check the browser console for any remaining issues');
}

main().catch(console.error);
