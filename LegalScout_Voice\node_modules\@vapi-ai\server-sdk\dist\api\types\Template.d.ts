/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface Template {
    details?: Vapi.TemplateDetails;
    providerDetails?: Vapi.TemplateProviderDetails;
    metadata?: Vapi.ToolTemplateMetadata;
    visibility?: Vapi.TemplateVisibility;
    type: "tool";
    /** The name of the template. This is just for your own reference. */
    name?: string;
    provider?: Vapi.TemplateProvider;
    /** The unique identifier for the template. */
    id: string;
    /** The unique identifier for the organization that this template belongs to. */
    orgId: string;
    /** The ISO 8601 date-time string of when the template was created. */
    createdAt: string;
    /** The ISO 8601 date-time string of when the template was last updated. */
    updatedAt: string;
}
