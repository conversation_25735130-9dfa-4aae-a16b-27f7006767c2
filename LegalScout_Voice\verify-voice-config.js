#!/usr/bin/env node

/**
 * <PERSON>ript to verify that all voice configurations have been updated to use OpenAI Echo voice
 */

import fs from 'fs';
import path from 'path';

const filesToCheck = [
  'src/services/vapiConfigService.js',
  'src/config/attorneys.js',
  'src/components/dashboard/VoiceAssistantConfig.jsx',
  'src/services/vapiAssistantService.js',
  'src/utils/vapiAssistantUtils.js',
  'src/services/vapiAssistantManager.js',
  'src/hooks/useVapiCall.js',
  'src/constants/vapiConstants.js',
  'src/config/vapiDefaults.js',
  'public/standalone-attorney-manager.js',
  '.env.development'
];

console.log('🔍 Verifying Voice Configuration Updates...\n');

let allGood = true;
const issues = [];

for (const filePath of filesToCheck) {
  try {
    const fullPath = path.resolve(filePath);
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // Check for ElevenLabs references
    const elevenLabsMatches = content.match(/(11labs|elevenlabs|sarah)/gi);
    const openAIMatches = content.match(/(openai.*echo|echo.*openai|voiceId.*echo|echo.*voiceId)/gi);
    
    console.log(`📄 ${filePath}:`);
    
    if (elevenLabsMatches) {
      console.log(`  ❌ Found ElevenLabs references: ${elevenLabsMatches.join(', ')}`);
      issues.push(`${filePath}: ${elevenLabsMatches.join(', ')}`);
      allGood = false;
    } else {
      console.log(`  ✅ No ElevenLabs references found`);
    }
    
    if (openAIMatches) {
      console.log(`  ✅ Found OpenAI Echo references: ${openAIMatches.length} matches`);
    }
    
    console.log('');
    
  } catch (error) {
    console.log(`  ⚠️  Could not read file: ${error.message}\n`);
  }
}

console.log('📊 Summary:');
if (allGood) {
  console.log('🎉 All voice configurations have been successfully updated to use OpenAI Echo voice!');
} else {
  console.log('❌ Some files still contain ElevenLabs references:');
  issues.forEach(issue => console.log(`   • ${issue}`));
  console.log('\n💡 These may be in comments or documentation and might be acceptable.');
}

console.log('\n🔧 Key Changes Made:');
console.log('   • Default voice provider: 11labs → openai');
console.log('   • Default voice ID: sarah → echo');
console.log('   • All assistant configurations updated');
console.log('   • Environment variables updated');
console.log('   • Fallback configurations updated');

console.log('\n✅ Voice configuration verification complete!');
