#!/usr/bin/env node

/**
 * Comprehensive Vapi Call Orchestration Test
 * Tests both client-side and server-side Vapi integration
 * Based on latest Vapi.ai documentation (2024)
 */

import https from 'https';

// Configuration from your .env
const API_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
const BASE_URL = 'https://api.vapi.ai';

console.log('🎯 Vapi Call Orchestration Test');
console.log('================================\n');

// Helper function for API requests
function makeRequest(path, options = {}) {
  return new Promise((resolve, reject) => {
    const url = `${BASE_URL}${path}`;
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'User-Agent': 'LegalScout-Orchestration-Test/1.0',
        ...options.headers
      }
    }, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const parsed = data ? JSON.parse(data) : null;
          resolve({ 
            status: res.statusCode, 
            data: parsed, 
            raw: data,
            headers: res.headers 
          });
        } catch (e) {
          resolve({ 
            status: res.statusCode, 
            data: null, 
            raw: data,
            headers: res.headers 
          });
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    req.end();
  });
}

// Test 1: Validate API Authentication
async function testAuthentication() {
  console.log('📋 Test 1: API Authentication');
  console.log('-'.repeat(40));

  try {
    const response = await makeRequest('/account');
    
    if (response.status === 200) {
      console.log('✅ Authentication: SUCCESS');
      if (response.data?.email) {
        console.log(`   Account: ${response.data.email}`);
      }
      if (response.data?.id) {
        console.log(`   Account ID: ${response.data.id}`);
      }
      return true;
    } else if (response.status === 401) {
      console.log('❌ Authentication: FAILED - Invalid API key');
      return false;
    } else {
      console.log(`❌ Authentication: FAILED - HTTP ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Authentication: ERROR - ${error.message}`);
    return false;
  }
}

// Test 2: Validate Assistant Configuration
async function testAssistantConfiguration() {
  console.log('\n📋 Test 2: Assistant Configuration');
  console.log('-'.repeat(40));

  try {
    const response = await makeRequest(`/assistant/${ASSISTANT_ID}`);
    
    if (response.status === 200 && response.data) {
      const assistant = response.data;
      console.log('✅ Assistant: ACCESSIBLE');
      console.log(`   Name: ${assistant.name || 'Unnamed'}`);
      
      // Validate critical configuration according to latest docs
      const validations = [
        { 
          name: 'Model Configuration', 
          check: assistant.model?.provider && assistant.model?.model,
          value: assistant.model ? `${assistant.model.provider}/${assistant.model.model}` : 'Missing'
        },
        { 
          name: 'Voice Configuration', 
          check: assistant.voice?.provider && assistant.voice?.voiceId,
          value: assistant.voice ? `${assistant.voice.provider}/${assistant.voice.voiceId}` : 'Missing'
        },
        { 
          name: 'Transcriber', 
          check: assistant.transcriber?.provider,
          value: assistant.transcriber?.provider || 'Missing'
        },
        { 
          name: 'First Message', 
          check: assistant.firstMessage,
          value: assistant.firstMessage ? 'Set' : 'Missing'
        }
      ];

      let allValid = true;
      validations.forEach(validation => {
        if (validation.check) {
          console.log(`   ✅ ${validation.name}: ${validation.value}`);
        } else {
          console.log(`   ❌ ${validation.name}: ${validation.value} (CRITICAL)`);
          allValid = false;
        }
      });

      return allValid;
    } else if (response.status === 404) {
      console.log('❌ Assistant: NOT FOUND');
      console.log('   Check if the assistant ID exists in your Vapi dashboard');
      return false;
    } else {
      console.log(`❌ Assistant: HTTP ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Assistant: ERROR - ${error.message}`);
    return false;
  }
}

// Test 3: Test Web Call Creation (Client-side approach)
async function testWebCallCreation() {
  console.log('\n📋 Test 3: Web Call Creation');
  console.log('-'.repeat(40));

  try {
    // According to latest docs, web calls use assistantId directly
    const callConfig = {
      type: 'webCall',
      assistantId: ASSISTANT_ID,
      // Optional metadata for tracking
      metadata: {
        source: 'orchestration-test',
        timestamp: new Date().toISOString()
      }
    };

    const response = await makeRequest('/call', {
      method: 'POST',
      body: callConfig
    });

    if (response.status === 201 || response.status === 200) {
      console.log('✅ Web Call Creation: SUCCESS');
      if (response.data?.id) {
        console.log(`   Call ID: ${response.data.id}`);
        console.log(`   Status: ${response.data.status || 'Unknown'}`);
        
        // Immediately end the test call to avoid charges
        try {
          await makeRequest(`/call/${response.data.id}`, {
            method: 'PATCH',
            body: { status: 'ended' }
          });
          console.log('   ✅ Test call ended successfully');
        } catch (endError) {
          console.log('   ⚠️ Could not end test call (not critical)');
        }
        
        return { success: true, callId: response.data.id };
      }
      return { success: true };
    } else {
      console.log(`❌ Web Call Creation: FAILED - HTTP ${response.status}`);
      if (response.data?.message) {
        console.log(`   Error: ${response.data.message}`);
      }
      return { success: false, error: response.data };
    }
  } catch (error) {
    console.log(`❌ Web Call Creation: ERROR - ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Test 4: Test Server-side Call Management
async function testServerSideManagement() {
  console.log('\n📋 Test 4: Server-side Call Management');
  console.log('-'.repeat(40));

  try {
    // List recent calls
    const listResponse = await makeRequest('/call?limit=5');
    
    if (listResponse.status === 200) {
      console.log('✅ Call Listing: SUCCESS');
      const calls = Array.isArray(listResponse.data) ? listResponse.data : [];
      console.log(`   Recent calls: ${calls.length}`);
      
      if (calls.length > 0) {
        const recentCall = calls[0];
        console.log(`   Latest call: ${recentCall.id} (${recentCall.status})`);
        
        // Test call retrieval
        const getResponse = await makeRequest(`/call/${recentCall.id}`);
        if (getResponse.status === 200) {
          console.log('   ✅ Call Retrieval: SUCCESS');
          return true;
        } else {
          console.log('   ❌ Call Retrieval: FAILED');
          return false;
        }
      } else {
        console.log('   ℹ️ No recent calls found (this is normal for new accounts)');
        return true;
      }
    } else {
      console.log(`❌ Call Listing: FAILED - HTTP ${listResponse.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Server-side Management: ERROR - ${error.message}`);
    return false;
  }
}

// Test 5: Validate Client-side SDK Compatibility
async function testClientSDKCompatibility() {
  console.log('\n📋 Test 5: Client SDK Compatibility');
  console.log('-'.repeat(40));

  try {
    // Test if the Web SDK CDN is accessible
    const cdnResponse = await makeRequest('https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/package.json');
    
    if (cdnResponse.status === 200) {
      console.log('✅ Vapi Web SDK CDN: ACCESSIBLE');
      if (cdnResponse.data?.version) {
        console.log(`   Latest version: ${cdnResponse.data.version}`);
      }
    } else {
      console.log('❌ Vapi Web SDK CDN: NOT ACCESSIBLE');
      console.log('   This may cause client-side initialization issues');
    }

    // Check if your current configuration matches latest SDK requirements
    console.log('\n   📋 SDK Configuration Validation:');
    console.log(`   ✅ API Key format: Valid (${API_KEY.length} chars)`);
    console.log(`   ✅ Assistant ID format: Valid (${ASSISTANT_ID.length} chars)`);
    console.log('   ✅ HTTPS endpoints: Required for WebRTC (✓)');
    
    return true;
  } catch (error) {
    console.log(`❌ Client SDK Compatibility: ERROR - ${error.message}`);
    return false;
  }
}

// Main test runner
async function runOrchestrationTests() {
  console.log(`🔑 Using API Key: ${API_KEY.substring(0, 8)}...`);
  console.log(`🤖 Testing Assistant: ${ASSISTANT_ID.substring(0, 8)}...\n`);

  const results = {
    authentication: false,
    assistant: false,
    webCall: false,
    serverSide: false,
    clientSDK: false
  };

  // Run all tests
  results.authentication = await testAuthentication();
  results.assistant = await testAssistantConfiguration();
  results.webCall = (await testWebCallCreation()).success;
  results.serverSide = await testServerSideManagement();
  results.clientSDK = await testClientSDKCompatibility();

  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('🎯 ORCHESTRATION TEST SUMMARY');
  console.log('='.repeat(50));

  const testResults = [
    { name: 'API Authentication', passed: results.authentication, critical: true },
    { name: 'Assistant Configuration', passed: results.assistant, critical: true },
    { name: 'Web Call Creation', passed: results.webCall, critical: true },
    { name: 'Server-side Management', passed: results.serverSide, critical: false },
    { name: 'Client SDK Compatibility', passed: results.clientSDK, critical: false }
  ];

  let criticalIssues = 0;
  testResults.forEach(test => {
    const status = test.passed ? '✅ PASS' : '❌ FAIL';
    const critical = test.critical ? ' (CRITICAL)' : '';
    console.log(`${status} ${test.name}${critical}`);
    
    if (!test.passed && test.critical) {
      criticalIssues++;
    }
  });

  console.log('\n📊 FINAL VERDICT:');
  if (criticalIssues === 0) {
    console.log('🎉 ALL CRITICAL TESTS PASSED - Your Vapi orchestration is ready!');
    console.log('\n💡 Next Steps:');
    console.log('   1. Test voice calls in your browser');
    console.log('   2. Verify microphone permissions');
    console.log('   3. Check browser console for any client-side errors');
  } else {
    console.log(`❌ ${criticalIssues} CRITICAL ISSUE(S) FOUND - Fix these before proceeding`);
    console.log('\n🔧 Recommended Actions:');
    if (!results.authentication) {
      console.log('   1. Verify your API key in the Vapi dashboard');
    }
    if (!results.assistant) {
      console.log('   2. Check assistant configuration in Vapi dashboard');
    }
    if (!results.webCall) {
      console.log('   3. Review call creation parameters');
    }
  }

  console.log('\n✅ Orchestration test completed!');
  return criticalIssues === 0;
}

// Run the tests
runOrchestrationTests().catch(error => {
  console.error('💥 Test suite failed:', error);
  process.exit(1);
});
