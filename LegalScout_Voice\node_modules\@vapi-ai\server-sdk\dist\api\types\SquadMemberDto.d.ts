/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as Vapi from "../index";
export interface SquadMemberDto {
    /** This is the assistant that will be used for the call. To use a transient assistant, use `assistant` instead. */
    assistantId?: string;
    /** This is the assistant that will be used for the call. To use an existing assistant, use `assistantId` instead. */
    assistant?: Vapi.CreateAssistantDto;
    /** This can be used to override the assistant's settings and provide values for it's template variables. */
    assistantOverrides?: Vapi.AssistantOverrides;
    /**
     * These are the others assistants that this assistant can transfer to.
     *
     * If the assistant already has transfer call tool, these destinations are just appended to existing ones.
     */
    assistantDestinations?: Vapi.TransferDestinationAssistant[];
}
