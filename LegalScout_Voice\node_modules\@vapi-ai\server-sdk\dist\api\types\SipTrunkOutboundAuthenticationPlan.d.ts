/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface SipTrunkOutboundAuthenticationPlan {
    /** This is not returned in the API. */
    authPassword?: string;
    authUsername?: string;
    /** This can be used to configure if SIP register is required by the SIP trunk. If not provided, no SIP registration will be attempted. */
    sipRegisterPlan?: Vapi.SipTrunkOutboundSipRegisterPlan;
}
