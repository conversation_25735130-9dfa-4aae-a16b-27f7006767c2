/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the language that will be set for the transcription. The list of languages Whisper supports can be found here: https://github.com/openai/whisper/blob/main/whisper/tokenizer.py
 */
export type TalkscriberTranscriberLanguage = "en" | "zh" | "de" | "es" | "ru" | "ko" | "fr" | "ja" | "pt" | "tr" | "pl" | "ca" | "nl" | "ar" | "sv" | "it" | "id" | "hi" | "fi" | "vi" | "he" | "uk" | "el" | "ms" | "cs" | "ro" | "da" | "hu" | "ta" | "no" | "th" | "ur" | "hr" | "bg" | "lt" | "la" | "mi" | "ml" | "cy" | "sk" | "te" | "fa" | "lv" | "bn" | "sr" | "az" | "sl" | "kn" | "et" | "mk" | "br" | "eu" | "is" | "hy" | "ne" | "mn" | "bs" | "kk" | "sq" | "sw" | "gl" | "mr" | "pa" | "si" | "km" | "sn" | "yo" | "so" | "af" | "oc" | "ka" | "be" | "tg" | "sd" | "gu" | "am" | "yi" | "lo" | "uz" | "fo" | "ht" | "ps" | "tk" | "nn" | "mt" | "sa" | "lb" | "my" | "bo" | "tl" | "mg" | "as" | "tt" | "haw" | "ln" | "ha" | "ba" | "jw" | "su" | "yue";
export declare const TalkscriberTranscriberLanguage: {
    readonly En: "en";
    readonly Zh: "zh";
    readonly De: "de";
    readonly Es: "es";
    readonly Ru: "ru";
    readonly Ko: "ko";
    readonly Fr: "fr";
    readonly Ja: "ja";
    readonly Pt: "pt";
    readonly Tr: "tr";
    readonly Pl: "pl";
    readonly Ca: "ca";
    readonly Nl: "nl";
    readonly Ar: "ar";
    readonly Sv: "sv";
    readonly It: "it";
    readonly Id: "id";
    readonly Hi: "hi";
    readonly Fi: "fi";
    readonly Vi: "vi";
    readonly He: "he";
    readonly Uk: "uk";
    readonly El: "el";
    readonly Ms: "ms";
    readonly Cs: "cs";
    readonly Ro: "ro";
    readonly Da: "da";
    readonly Hu: "hu";
    readonly Ta: "ta";
    readonly No: "no";
    readonly Th: "th";
    readonly Ur: "ur";
    readonly Hr: "hr";
    readonly Bg: "bg";
    readonly Lt: "lt";
    readonly La: "la";
    readonly Mi: "mi";
    readonly Ml: "ml";
    readonly Cy: "cy";
    readonly Sk: "sk";
    readonly Te: "te";
    readonly Fa: "fa";
    readonly Lv: "lv";
    readonly Bn: "bn";
    readonly Sr: "sr";
    readonly Az: "az";
    readonly Sl: "sl";
    readonly Kn: "kn";
    readonly Et: "et";
    readonly Mk: "mk";
    readonly Br: "br";
    readonly Eu: "eu";
    readonly Is: "is";
    readonly Hy: "hy";
    readonly Ne: "ne";
    readonly Mn: "mn";
    readonly Bs: "bs";
    readonly Kk: "kk";
    readonly Sq: "sq";
    readonly Sw: "sw";
    readonly Gl: "gl";
    readonly Mr: "mr";
    readonly Pa: "pa";
    readonly Si: "si";
    readonly Km: "km";
    readonly Sn: "sn";
    readonly Yo: "yo";
    readonly So: "so";
    readonly Af: "af";
    readonly Oc: "oc";
    readonly Ka: "ka";
    readonly Be: "be";
    readonly Tg: "tg";
    readonly Sd: "sd";
    readonly Gu: "gu";
    readonly Am: "am";
    readonly Yi: "yi";
    readonly Lo: "lo";
    readonly Uz: "uz";
    readonly Fo: "fo";
    readonly Ht: "ht";
    readonly Ps: "ps";
    readonly Tk: "tk";
    readonly Nn: "nn";
    readonly Mt: "mt";
    readonly Sa: "sa";
    readonly Lb: "lb";
    readonly My: "my";
    readonly Bo: "bo";
    readonly Tl: "tl";
    readonly Mg: "mg";
    readonly As: "as";
    readonly Tt: "tt";
    readonly Haw: "haw";
    readonly Ln: "ln";
    readonly Ha: "ha";
    readonly Ba: "ba";
    readonly Jw: "jw";
    readonly Su: "su";
    readonly Yue: "yue";
};
