/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface ServerMessageVoiceRequest {
    /**
     * This is the phone number associated with the call.
     *
     * This matches one of the following:
     * - `call.phoneNumber`,
     * - `call.phoneNumberId`.
     */
    phoneNumber?: Vapi.ServerMessageVoiceRequestPhoneNumber;
    /**
     * This is the type of the message. "voice-request" is sent when using `assistant.voice={ "type": "custom-voice" }`.
     *
     * Here is what the request will look like:
     *
     * POST https://{assistant.voice.server.url}
     * Content-Type: application/json
     *
     * {
     *   "messsage": {
     *     "type": "voice-request",
     *     "text": "Hello, world!",
     *     "sampleRate": 24000,
     *     ...other metadata about the call...
     *   }
     * }
     *
     * The expected response is 1-channel 16-bit raw PCM audio at the sample rate specified in the request. Here is how the response will be piped to the transport:
     * ```
     * response.on('data', (chunk: Buffer) => {
     *   outputStream.write(chunk);
     * });
     * ```
     */
    type: "voice-request";
    /** This is the timestamp of when the message was sent in milliseconds since Unix Epoch. */
    timestamp?: number;
    /**
     * This is a live version of the `call.artifact`.
     *
     * This matches what is stored on `call.artifact` after the call.
     */
    artifact?: Vapi.Artifact;
    /**
     * This is the assistant that is currently active. This is provided for convenience.
     *
     * This matches one of the following:
     * - `call.assistant`,
     * - `call.assistantId`,
     * - `call.squad[n].assistant`,
     * - `call.squad[n].assistantId`,
     * - `call.squadId->[n].assistant`,
     * - `call.squadId->[n].assistantId`.
     */
    assistant?: Vapi.CreateAssistantDto;
    /**
     * This is the customer associated with the call.
     *
     * This matches one of the following:
     * - `call.customer`,
     * - `call.customerId`.
     */
    customer?: Vapi.CreateCustomerDto;
    /**
     * This is the call object.
     *
     * This matches what was returned in POST /call.
     *
     * Note: This might get stale during the call. To get the latest call object, especially after the call is ended, use GET /call/:id.
     */
    call?: Vapi.Call;
    /** This is the text to be synthesized. */
    text: string;
    /** This is the sample rate to be synthesized. */
    sampleRate: number;
}
