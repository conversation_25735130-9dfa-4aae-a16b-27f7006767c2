/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface S3Credential {
    /** Credential provider. Only allowed value is s3 */
    provider: "s3";
    /** AWS access key ID. */
    awsAccessKeyId: string;
    /** AWS access key secret. This is not returned in the API. */
    awsSecretAccessKey: string;
    /** AWS region in which the S3 bucket is located. */
    region: string;
    /** AWS S3 bucket name. */
    s3BucketName: string;
    /** The path prefix for the uploaded recording. Ex. "recordings/" */
    s3PathPrefix: string;
    /** This is the unique identifier for the credential. */
    id: string;
    /** This is the unique identifier for the org that this credential belongs to. */
    orgId: string;
    /** This is the ISO 8601 date-time string of when the credential was created. */
    createdAt: string;
    /** This is the ISO 8601 date-time string of when the assistant was last updated. */
    updatedAt: string;
    /** This is the name of credential. This is just for your reference. */
    name?: string;
}
