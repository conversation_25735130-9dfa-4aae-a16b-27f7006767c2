/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type SpeechmaticsTranscriberLanguage = "auto" | "ar" | "ba" | "eu" | "be" | "bn" | "bg" | "yue" | "ca" | "hr" | "cs" | "da" | "nl" | "en" | "eo" | "et" | "fi" | "fr" | "gl" | "de" | "el" | "he" | "hi" | "hu" | "id" | "ia" | "ga" | "it" | "ja" | "ko" | "lv" | "lt" | "ms" | "mt" | "cmn" | "mr" | "mn" | "no" | "fa" | "pl" | "pt" | "ro" | "ru" | "sk" | "sl" | "es" | "sw" | "sv" | "ta" | "th" | "tr" | "uk" | "ur" | "ug" | "vi" | "cy";
export declare const SpeechmaticsTranscriberLanguage: {
    readonly Auto: "auto";
    readonly Ar: "ar";
    readonly Ba: "ba";
    readonly Eu: "eu";
    readonly Be: "be";
    readonly Bn: "bn";
    readonly Bg: "bg";
    readonly Yue: "yue";
    readonly Ca: "ca";
    readonly Hr: "hr";
    readonly Cs: "cs";
    readonly Da: "da";
    readonly Nl: "nl";
    readonly En: "en";
    readonly Eo: "eo";
    readonly Et: "et";
    readonly Fi: "fi";
    readonly Fr: "fr";
    readonly Gl: "gl";
    readonly De: "de";
    readonly El: "el";
    readonly He: "he";
    readonly Hi: "hi";
    readonly Hu: "hu";
    readonly Id: "id";
    readonly Ia: "ia";
    readonly Ga: "ga";
    readonly It: "it";
    readonly Ja: "ja";
    readonly Ko: "ko";
    readonly Lv: "lv";
    readonly Lt: "lt";
    readonly Ms: "ms";
    readonly Mt: "mt";
    readonly Cmn: "cmn";
    readonly Mr: "mr";
    readonly Mn: "mn";
    readonly No: "no";
    readonly Fa: "fa";
    readonly Pl: "pl";
    readonly Pt: "pt";
    readonly Ro: "ro";
    readonly Ru: "ru";
    readonly Sk: "sk";
    readonly Sl: "sl";
    readonly Es: "es";
    readonly Sw: "sw";
    readonly Sv: "sv";
    readonly Ta: "ta";
    readonly Th: "th";
    readonly Tr: "tr";
    readonly Uk: "uk";
    readonly Ur: "ur";
    readonly Ug: "ug";
    readonly Vi: "vi";
    readonly Cy: "cy";
};
