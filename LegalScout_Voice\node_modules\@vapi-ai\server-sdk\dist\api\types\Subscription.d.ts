/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface Subscription {
    /** This is the unique identifier for the subscription. */
    id: string;
    /** This is the timestamp when the subscription was created. */
    createdAt: string;
    /** This is the timestamp when the subscription was last updated. */
    updatedAt: string;
    /** This is the type / tier of the subscription. */
    type: Vapi.SubscriptionType;
    /**
     * This is the status of the subscription. Past due subscriptions are subscriptions
     * with past due payments.
     */
    status: Vapi.SubscriptionStatus;
    /**
     * This is the number of credits the subscription currently has.
     *
     * Note: This is a string to avoid floating point precision issues.
     */
    credits: string;
    /** This is the total number of active calls (concurrency) across all orgs under this subscription. */
    concurrencyCounter: number;
    /** This is the default concurrency limit for the subscription. */
    concurrencyLimitIncluded: number;
    /** This is the number of free phone numbers the subscription has */
    phoneNumbersCounter?: number;
    /** This is the maximum number of free phone numbers the subscription can have */
    phoneNumbersIncluded?: number;
    /** This is the purchased add-on concurrency limit for the subscription. */
    concurrencyLimitPurchased: number;
    /** This is the ID of the monthly job that charges for subscription add ons and phone numbers. */
    monthlyChargeScheduleId?: number;
    /**
     * This is the ID of the monthly job that checks whether the credit balance of the subscription
     * is sufficient for the monthly charge.
     */
    monthlyCreditCheckScheduleId?: number;
    /** This is the Stripe customer ID. */
    stripeCustomerId?: string;
    /** This is the Stripe payment ID. */
    stripePaymentMethodId?: string;
    /** If this flag is true, then the user has purchased slack support. */
    slackSupportEnabled?: boolean;
    /** If this subscription has a slack support subscription, the slack channel's ID will be stored here. */
    slackChannelId?: string;
    /**
     * This is the HIPAA enabled flag for the subscription. It determines whether orgs under this
     * subscription have the option to enable HIPAA compliance.
     */
    hipaaEnabled?: boolean;
    /** This is the ID for the Common Paper agreement outlining the HIPAA contract. */
    hipaaCommonPaperAgreementId?: string;
    /**
     * This is the Stripe fingerprint of the payment method (card). It allows us
     * to detect users who try to abuse our system through multiple sign-ups.
     */
    stripePaymentMethodFingerprint?: string;
    /** This is the customer's email on Stripe. */
    stripeCustomerEmail?: string;
    /** This is the email of the referrer for the subscription. */
    referredByEmail?: string;
    /** This is the auto reload plan configured for the subscription. */
    autoReloadPlan?: Vapi.AutoReloadPlan;
    /** The number of minutes included in the subscription. */
    minutesIncluded?: number;
    /** The number of minutes used in the subscription. */
    minutesUsed?: number;
    /** This is the timestamp at which the number of monthly free minutes is scheduled to reset at. */
    minutesUsedNextResetAt?: string;
    /** The per minute charge on minutes that exceed the included minutes. Enterprise only. */
    minutesOverageCost?: number;
    /** The list of providers included in the subscription. Enterprise only. */
    providersIncluded?: string[];
    /** The maximum number of outbound calls this subscription may make in a day. Resets every night. */
    outboundCallsDailyLimit?: number;
    /** The current number of outbound calls the subscription has made in the current day. */
    outboundCallsCounter?: number;
    /** This is the timestamp at which the outbound calls counter is scheduled to reset at. */
    outboundCallsCounterNextResetAt?: string;
    /** This is the IDs of the coupons applicable to this subscription. */
    couponIds?: string[];
    /** This is the number of credits left obtained from a coupon. */
    couponUsageLeft?: string;
    /** This is the invoice plan for the subscription. */
    invoicePlan?: Vapi.InvoicePlan;
    /**
     * This is the PCI enabled flag for the subscription. It determines whether orgs under this
     * subscription have the option to enable PCI compliance.
     */
    pciEnabled?: boolean;
    /** This is the ID for the Common Paper agreement outlining the PCI contract. */
    pciCommonPaperAgreementId?: string;
}
