/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface Token {
    /** This is the tag for the token. It represents its scope. */
    tag?: Vapi.TokenTag;
    /** This is the unique identifier for the token. */
    id: string;
    /** This is unique identifier for the org that this token belongs to. */
    orgId: string;
    /** This is the ISO 8601 date-time string of when the token was created. */
    createdAt: string;
    /** This is the ISO 8601 date-time string of when the token was last updated. */
    updatedAt: string;
    /** This is the token key. */
    value: string;
    /** This is the name of the token. This is just for your own reference. */
    name?: string;
    /** This are the restrictions for the token. */
    restrictions?: Vapi.TokenRestrictions;
}
