/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the time step for aggregations.
 *
 * If not provided, defaults to returning for the entire time range.
 */
export type TimeRangeStep = "second" | "minute" | "hour" | "day" | "week" | "month" | "quarter" | "year" | "decade" | "century" | "millennium";
export declare const TimeRangeStep: {
    readonly Second: "second";
    readonly Minute: "minute";
    readonly Hour: "hour";
    readonly Day: "day";
    readonly Week: "week";
    readonly Month: "month";
    readonly Quarter: "quarter";
    readonly Year: "year";
    readonly Decade: "decade";
    readonly Century: "century";
    readonly Millennium: "millennium";
};
