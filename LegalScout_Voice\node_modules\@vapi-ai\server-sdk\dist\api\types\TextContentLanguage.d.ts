/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type TextContentLanguage = "aa" | "ab" | "ae" | "af" | "ak" | "am" | "an" | "ar" | "as" | "av" | "ay" | "az" | "ba" | "be" | "bg" | "bh" | "bi" | "bm" | "bn" | "bo" | "br" | "bs" | "ca" | "ce" | "ch" | "co" | "cr" | "cs" | "cu" | "cv" | "cy" | "da" | "de" | "dv" | "dz" | "ee" | "el" | "en" | "eo" | "es" | "et" | "eu" | "fa" | "ff" | "fi" | "fj" | "fo" | "fr" | "fy" | "ga" | "gd" | "gl" | "gn" | "gu" | "gv" | "ha" | "he" | "hi" | "ho" | "hr" | "ht" | "hu" | "hy" | "hz" | "ia" | "id" | "ie" | "ig" | "ii" | "ik" | "io" | "is" | "it" | "iu" | "ja" | "jv" | "ka" | "kg" | "ki" | "kj" | "kk" | "kl" | "km" | "kn" | "ko" | "kr" | "ks" | "ku" | "kv" | "kw" | "ky" | "la" | "lb" | "lg" | "li" | "ln" | "lo" | "lt" | "lu" | "lv" | "mg" | "mh" | "mi" | "mk" | "ml" | "mn" | "mr" | "ms" | "mt" | "my" | "na" | "nb" | "nd" | "ne" | "ng" | "nl" | "nn" | "no" | "nr" | "nv" | "ny" | "oc" | "oj" | "om" | "or" | "os" | "pa" | "pi" | "pl" | "ps" | "pt" | "qu" | "rm" | "rn" | "ro" | "ru" | "rw" | "sa" | "sc" | "sd" | "se" | "sg" | "si" | "sk" | "sl" | "sm" | "sn" | "so" | "sq" | "sr" | "ss" | "st" | "su" | "sv" | "sw" | "ta" | "te" | "tg" | "th" | "ti" | "tk" | "tl" | "tn" | "to" | "tr" | "ts" | "tt" | "tw" | "ty" | "ug" | "uk" | "ur" | "uz" | "ve" | "vi" | "vo" | "wa" | "wo" | "xh" | "yi" | "yue" | "yo" | "za" | "zh" | "zu";
export declare const TextContentLanguage: {
    readonly Aa: "aa";
    readonly Ab: "ab";
    readonly Ae: "ae";
    readonly Af: "af";
    readonly Ak: "ak";
    readonly Am: "am";
    readonly An: "an";
    readonly Ar: "ar";
    readonly As: "as";
    readonly Av: "av";
    readonly Ay: "ay";
    readonly Az: "az";
    readonly Ba: "ba";
    readonly Be: "be";
    readonly Bg: "bg";
    readonly Bh: "bh";
    readonly Bi: "bi";
    readonly Bm: "bm";
    readonly Bn: "bn";
    readonly Bo: "bo";
    readonly Br: "br";
    readonly Bs: "bs";
    readonly Ca: "ca";
    readonly Ce: "ce";
    readonly Ch: "ch";
    readonly Co: "co";
    readonly Cr: "cr";
    readonly Cs: "cs";
    readonly Cu: "cu";
    readonly Cv: "cv";
    readonly Cy: "cy";
    readonly Da: "da";
    readonly De: "de";
    readonly Dv: "dv";
    readonly Dz: "dz";
    readonly Ee: "ee";
    readonly El: "el";
    readonly En: "en";
    readonly Eo: "eo";
    readonly Es: "es";
    readonly Et: "et";
    readonly Eu: "eu";
    readonly Fa: "fa";
    readonly Ff: "ff";
    readonly Fi: "fi";
    readonly Fj: "fj";
    readonly Fo: "fo";
    readonly Fr: "fr";
    readonly Fy: "fy";
    readonly Ga: "ga";
    readonly Gd: "gd";
    readonly Gl: "gl";
    readonly Gn: "gn";
    readonly Gu: "gu";
    readonly Gv: "gv";
    readonly Ha: "ha";
    readonly He: "he";
    readonly Hi: "hi";
    readonly Ho: "ho";
    readonly Hr: "hr";
    readonly Ht: "ht";
    readonly Hu: "hu";
    readonly Hy: "hy";
    readonly Hz: "hz";
    readonly Ia: "ia";
    readonly Id: "id";
    readonly Ie: "ie";
    readonly Ig: "ig";
    readonly Ii: "ii";
    readonly Ik: "ik";
    readonly Io: "io";
    readonly Is: "is";
    readonly It: "it";
    readonly Iu: "iu";
    readonly Ja: "ja";
    readonly Jv: "jv";
    readonly Ka: "ka";
    readonly Kg: "kg";
    readonly Ki: "ki";
    readonly Kj: "kj";
    readonly Kk: "kk";
    readonly Kl: "kl";
    readonly Km: "km";
    readonly Kn: "kn";
    readonly Ko: "ko";
    readonly Kr: "kr";
    readonly Ks: "ks";
    readonly Ku: "ku";
    readonly Kv: "kv";
    readonly Kw: "kw";
    readonly Ky: "ky";
    readonly La: "la";
    readonly Lb: "lb";
    readonly Lg: "lg";
    readonly Li: "li";
    readonly Ln: "ln";
    readonly Lo: "lo";
    readonly Lt: "lt";
    readonly Lu: "lu";
    readonly Lv: "lv";
    readonly Mg: "mg";
    readonly Mh: "mh";
    readonly Mi: "mi";
    readonly Mk: "mk";
    readonly Ml: "ml";
    readonly Mn: "mn";
    readonly Mr: "mr";
    readonly Ms: "ms";
    readonly Mt: "mt";
    readonly My: "my";
    readonly Na: "na";
    readonly Nb: "nb";
    readonly Nd: "nd";
    readonly Ne: "ne";
    readonly Ng: "ng";
    readonly Nl: "nl";
    readonly Nn: "nn";
    readonly No: "no";
    readonly Nr: "nr";
    readonly Nv: "nv";
    readonly Ny: "ny";
    readonly Oc: "oc";
    readonly Oj: "oj";
    readonly Om: "om";
    readonly Or: "or";
    readonly Os: "os";
    readonly Pa: "pa";
    readonly Pi: "pi";
    readonly Pl: "pl";
    readonly Ps: "ps";
    readonly Pt: "pt";
    readonly Qu: "qu";
    readonly Rm: "rm";
    readonly Rn: "rn";
    readonly Ro: "ro";
    readonly Ru: "ru";
    readonly Rw: "rw";
    readonly Sa: "sa";
    readonly Sc: "sc";
    readonly Sd: "sd";
    readonly Se: "se";
    readonly Sg: "sg";
    readonly Si: "si";
    readonly Sk: "sk";
    readonly Sl: "sl";
    readonly Sm: "sm";
    readonly Sn: "sn";
    readonly So: "so";
    readonly Sq: "sq";
    readonly Sr: "sr";
    readonly Ss: "ss";
    readonly St: "st";
    readonly Su: "su";
    readonly Sv: "sv";
    readonly Sw: "sw";
    readonly Ta: "ta";
    readonly Te: "te";
    readonly Tg: "tg";
    readonly Th: "th";
    readonly Ti: "ti";
    readonly Tk: "tk";
    readonly Tl: "tl";
    readonly Tn: "tn";
    readonly To: "to";
    readonly Tr: "tr";
    readonly Ts: "ts";
    readonly Tt: "tt";
    readonly Tw: "tw";
    readonly Ty: "ty";
    readonly Ug: "ug";
    readonly Uk: "uk";
    readonly Ur: "ur";
    readonly Uz: "uz";
    readonly Ve: "ve";
    readonly Vi: "vi";
    readonly Vo: "vo";
    readonly Wa: "wa";
    readonly Wo: "wo";
    readonly Xh: "xh";
    readonly Yi: "yi";
    readonly Yue: "yue";
    readonly Yo: "yo";
    readonly Za: "za";
    readonly Zh: "zh";
    readonly Zu: "zu";
};
