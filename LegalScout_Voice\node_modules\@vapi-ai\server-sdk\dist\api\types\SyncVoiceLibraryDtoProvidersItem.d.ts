/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type SyncVoiceLibraryDtoProvidersItem = "vapi" | "11labs" | "azure" | "cartesia" | "custom-voice" | "deepgram" | "hume" | "lmnt" | "neuphonic" | "openai" | "playht" | "rime-ai" | "smallest-ai" | "tavus" | "sesame";
export declare const SyncVoiceLibraryDtoProvidersItem: {
    readonly Vapi: "vapi";
    readonly ElevenLabs: "11labs";
    readonly Azure: "azure";
    readonly Cartesia: "cartesia";
    readonly CustomVoice: "custom-voice";
    readonly Deepgram: "deepgram";
    readonly Hume: "hume";
    readonly Lmnt: "lmnt";
    readonly Neuphonic: "neuphonic";
    readonly Openai: "openai";
    readonly Playht: "playht";
    readonly RimeAi: "rime-ai";
    readonly SmallestAi: "smallest-ai";
    readonly <PERSON>: "tavus";
    readonly Sesame: "sesame";
};
