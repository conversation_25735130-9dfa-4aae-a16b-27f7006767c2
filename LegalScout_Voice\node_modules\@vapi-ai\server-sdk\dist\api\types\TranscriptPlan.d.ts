/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
export interface TranscriptPlan {
    /**
     * This determines whether the transcript is stored in `call.artifact.transcript`. Defaults to true.
     *
     * @default true
     */
    enabled?: boolean;
    /**
     * This is the name of the assistant in the transcript. Defaults to 'AI'.
     *
     * Usage:
     * - If you want to change the name of the assistant in the transcript, set this. Example, here is what the transcript would look like with `assistantName` set to 'Buyer':
     * ```
     * User: Hello, how are you?
     * Buyer: I'm fine.
     * User: Do you want to buy a car?
     * Buyer: No.
     * ```
     *
     * @default 'AI'
     */
    assistantName?: string;
    /**
     * This is the name of the user in the transcript. Defaults to 'User'.
     *
     * Usage:
     * - If you want to change the name of the user in the transcript, set this. Example, here is what the transcript would look like with `userName` set to 'Seller':
     * ```
     * Seller: Hello, how are you?
     * AI: I'm fine.
     * Seller: Do you want to buy a car?
     * AI: No.
     * ```
     *
     * @default 'User'
     */
    userName?: string;
}
