/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface ServerMessageStatusUpdate {
    /**
     * This is the phone number associated with the call.
     *
     * This matches one of the following:
     * - `call.phoneNumber`,
     * - `call.phoneNumberId`.
     */
    phoneNumber?: Vapi.ServerMessageStatusUpdatePhoneNumber;
    /** This is the type of the message. "status-update" is sent whenever the `call.status` changes. */
    type: "status-update";
    /** This is the status of the call. */
    status: Vapi.ServerMessageStatusUpdateStatus;
    /** This is the reason the call ended. This is only sent if the status is "ended". */
    endedReason?: Vapi.ServerMessageStatusUpdateEndedReason;
    /** These are the conversation messages of the call. This is only sent if the status is "forwarding". */
    messages?: Vapi.ServerMessageStatusUpdateMessagesItem[];
    /** These are the conversation messages of the call. This is only sent if the status is "forwarding". */
    messagesOpenAIFormatted?: Vapi.OpenAiMessage[];
    /** This is the destination the call is being transferred to. This is only sent if the status is "forwarding". */
    destination?: Vapi.ServerMessageStatusUpdateDestination;
    /** This is the timestamp of when the message was sent in milliseconds since Unix Epoch. */
    timestamp?: number;
    /**
     * This is a live version of the `call.artifact`.
     *
     * This matches what is stored on `call.artifact` after the call.
     */
    artifact?: Vapi.Artifact;
    /**
     * This is the assistant that is currently active. This is provided for convenience.
     *
     * This matches one of the following:
     * - `call.assistant`,
     * - `call.assistantId`,
     * - `call.squad[n].assistant`,
     * - `call.squad[n].assistantId`,
     * - `call.squadId->[n].assistant`,
     * - `call.squadId->[n].assistantId`.
     */
    assistant?: Vapi.CreateAssistantDto;
    /**
     * This is the customer associated with the call.
     *
     * This matches one of the following:
     * - `call.customer`,
     * - `call.customerId`.
     */
    customer?: Vapi.CreateCustomerDto;
    /**
     * This is the call object.
     *
     * This matches what was returned in POST /call.
     *
     * Note: This might get stale during the call. To get the latest call object, especially after the call is ended, use GET /call/:id.
     */
    call?: Vapi.Call;
    /** This is the transcript of the call. This is only sent if the status is "forwarding". */
    transcript?: string;
    /** This is the summary of the call. This is only sent if the status is "forwarding". */
    summary?: string;
    /**
     * This is the inbound phone call debugging artifacts. This is only sent if the status is "ended" and there was an error accepting the inbound phone call.
     *
     * This will include any errors related to the "assistant-request" if one was made.
     */
    inboundPhoneCallDebuggingArtifacts?: Record<string, unknown>;
}
