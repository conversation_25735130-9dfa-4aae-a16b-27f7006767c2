/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type SmallestAiVoiceIdEnum = "emily" | "jasmine" | "arman" | "james" | "mithali" | "aravind" | "raj" | "diya" | "raman" | "ananya" | "isha" | "william" | "aarav" | "monika" | "niharika" | "deepika" | "raghav" | "kajal" | "radhika" | "mansi" | "nisha" | "saurabh" | "pooja" | "saina" | "sanya";
export declare const SmallestAiVoiceIdEnum: {
    readonly <PERSON>: "emily";
    readonly <PERSON>: "jasmine";
    readonly <PERSON><PERSON>: "arman";
    readonly <PERSON>: "james";
    readonly <PERSON>: "mithali";
    readonly <PERSON>vind: "aravind";
    readonly <PERSON>: "raj";
    readonly <PERSON>ya: "diya";
    readonly <PERSON>n: "raman";
    readonly <PERSON><PERSON>: "ananya";
    readonly <PERSON>: "isha";
    readonly <PERSON>: "william";
    readonly <PERSON><PERSON>: "aarav";
    readonly <PERSON><PERSON>: "monika";
    readonly <PERSON>ka: "niharika";
    readonly <PERSON><PERSON>: "deepika";
    readonly <PERSON>ghav: "raghav";
    readonly Kajal: "kajal";
    readonly Radhika: "radhika";
    readonly <PERSON>i: "mansi";
    readonly Nisha: "nisha";
    readonly Saurabh: "saurabh";
    readonly Pooja: "pooja";
    readonly Saina: "saina";
    readonly Sanya: "sanya";
};
