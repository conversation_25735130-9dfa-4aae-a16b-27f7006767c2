<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Environment Variables</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .env-var {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .correct { border-color: #28a745; background-color: #d4edda; }
        .incorrect { border-color: #dc3545; background-color: #f8d7da; }
        .warning { border-color: #ffc107; background-color: #fff3cd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Environment Variables Test</h1>
        <p>This page will show what API key your application is actually using.</p>
        
        <div id="results"></div>
        
        <button onclick="testVapiConnection()" style="margin-top: 20px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            🔑 Test Current API Key
        </button>
    </div>

    <script>
        // This will show what the browser actually sees
        function checkEnvironmentVariables() {
            const results = document.getElementById('results');
            
            // Try to get the environment variable (this won't work in production, but will in dev)
            const envApiKey = window.VITE_VAPI_PUBLIC_KEY || 'Not accessible from browser';
            
            // The keys we're testing
            const oldKey = '310f0d43-27c2-47a5-a76d-e55171d024f7';
            const newKey = '6734febc-fc65-4669-93b0-929b31ff6564';
            
            let html = '<h3>Environment Variable Status:</h3>';
            
            // Check what the app would actually use
            html += `<div class="env-var">
                <strong>Browser Environment Variable:</strong><br>
                VITE_VAPI_PUBLIC_KEY = ${envApiKey}
            </div>`;
            
            // Determine status
            if (envApiKey === oldKey) {
                html += `<div class="env-var incorrect">
                    ❌ <strong>PROBLEM:</strong> Still using old API key (assistant ID)<br>
                    The development server needs to be restarted to pick up the new environment variable.
                </div>`;
            } else if (envApiKey === newKey) {
                html += `<div class="env-var correct">
                    ✅ <strong>GOOD:</strong> Using the correct API key<br>
                    Environment variable has been updated successfully.
                </div>`;
            } else {
                html += `<div class="env-var warning">
                    ⚠️ <strong>UNKNOWN:</strong> Using different key or not accessible<br>
                    This might be normal in production builds.
                </div>`;
            }
            
            html += `<h3>Reference:</h3>
            <div class="env-var">
                <strong>Old Key (Assistant ID):</strong> ${oldKey}<br>
                <strong>New Key (Real API Key):</strong> ${newKey}
            </div>`;
            
            results.innerHTML = html;
        }
        
        async function testVapiConnection() {
            const button = event.target;
            button.disabled = true;
            button.textContent = '🔄 Testing...';
            
            try {
                // Try to determine which key is being used by making a test API call
                const testKeys = [
                    { name: 'Current Environment', key: window.VITE_VAPI_PUBLIC_KEY },
                    { name: 'New Key', key: '6734febc-fc65-4669-93b0-929b31ff6564' },
                    { name: 'Old Key', key: '310f0d43-27c2-47a5-a76d-e55171d024f7' }
                ];
                
                let html = '<h3>API Connection Test Results:</h3>';
                
                for (const testKey of testKeys) {
                    if (!testKey.key || testKey.key === 'Not accessible from browser') {
                        html += `<div class="env-var warning">
                            ⚠️ <strong>${testKey.name}:</strong> Not available for testing
                        </div>`;
                        continue;
                    }
                    
                    try {
                        const response = await fetch('https://api.vapi.ai/assistant', {
                            headers: {
                                'Authorization': `Bearer ${testKey.key}`,
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            const count = Array.isArray(data) ? data.length : 'unknown';
                            html += `<div class="env-var correct">
                                ✅ <strong>${testKey.name}:</strong> Working! (${count} assistants found)
                            </div>`;
                        } else {
                            html += `<div class="env-var incorrect">
                                ❌ <strong>${testKey.name}:</strong> Failed (HTTP ${response.status})
                            </div>`;
                        }
                    } catch (error) {
                        html += `<div class="env-var incorrect">
                            ❌ <strong>${testKey.name}:</strong> Error - ${error.message}
                        </div>`;
                    }
                }
                
                document.getElementById('results').innerHTML += html;
                
            } catch (error) {
                document.getElementById('results').innerHTML += `<div class="env-var incorrect">
                    ❌ <strong>Test Error:</strong> ${error.message}
                </div>`;
            }
            
            button.disabled = false;
            button.textContent = '🔑 Test Current API Key';
        }
        
        // Run the check when page loads
        window.addEventListener('load', checkEnvironmentVariables);
    </script>
</body>
</html>
