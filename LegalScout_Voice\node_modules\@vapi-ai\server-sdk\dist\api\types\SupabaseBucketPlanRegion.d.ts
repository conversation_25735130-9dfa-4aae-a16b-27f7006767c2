/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * This is the S3 Region. It should look like us-east-1
 * It should be one of the supabase regions defined in the SUPABASE_REGION enum
 * Check https://supabase.com/docs/guides/platform/regions for up to date regions
 */
export type SupabaseBucketPlanRegion = "us-west-1" | "us-east-1" | "us-east-2" | "ca-central-1" | "eu-west-1" | "eu-west-2" | "eu-west-3" | "eu-central-1" | "eu-central-2" | "eu-north-1" | "ap-south-1" | "ap-southeast-1" | "ap-northeast-1" | "ap-northeast-2" | "ap-southeast-2" | "sa-east-1";
export declare const SupabaseBucketPlanRegion: {
    readonly UsWest1: "us-west-1";
    readonly UsEast1: "us-east-1";
    readonly UsEast2: "us-east-2";
    readonly CaCentral1: "ca-central-1";
    readonly EuWest1: "eu-west-1";
    readonly EuWest2: "eu-west-2";
    readonly EuWest3: "eu-west-3";
    readonly EuCentral1: "eu-central-1";
    readonly EuCentral2: "eu-central-2";
    readonly EuNorth1: "eu-north-1";
    readonly ApSouth1: "ap-south-1";
    readonly ApSoutheast1: "ap-southeast-1";
    readonly ApNortheast1: "ap-northeast-1";
    readonly ApNortheast2: "ap-northeast-2";
    readonly ApSoutheast2: "ap-southeast-2";
    readonly SaEast1: "sa-east-1";
};
