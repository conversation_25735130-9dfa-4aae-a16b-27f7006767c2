import { useState, useEffect, useCallback, useRef } from 'react';
import Vapi from '@vapi-ai/web';
import { vapiService } from '../services/VapiService';
import { getAttorneyConfigAsync } from '../config/attorneys';
import { createDebugger } from '../utils/debugConfig';
import { DEFAULT_ASSISTANT_ID, VAPI_TOOL_IDS, MAKE_WEBHOOK_URLS, CALL_STATUS } from '../constants/vapiConstants';

// Create debugger for hook
const debug = createDebugger('useVapiCall');

/**
 * Custom hook to manage Vapi call state and functionality
 *
 * @param {Object} options - Options for the call
 * @param {string} options.subdomain - The attorney subdomain
 * @param {Function} options.onEndCall - Function to call when the call ends
 * @param {Object} options.customInstructions - Custom instructions for the Vapi call
 * @param {Object} options.assistantOverrides - Overrides for the Vapi assistant configuration
 * @param {string} options.assistantId - Explicit assistant ID to use (overrides any ID in customInstructions)
 * @returns {Object} Call state and functions
 */
const useVapiCall = ({
  subdomain = 'default',
  onEndCall,
  customInstructions = null,
  assistantOverrides = null,
  assistantId = null
}) => {
  // Only log essential information about the assistant ID
  console.log('useVapiCall: Using assistant ID:', assistantId || 'Not explicitly provided');
  const [vapi, setVapi] = useState(null);
  const [status, setStatus] = useState('idle');
  const [dossierData, setDossierData] = useState({});
  const [messageHistory, setMessageHistory] = useState([]);
  const [volumeLevel, setVolumeLevel] = useState(0);
  const [assistantIsSpeaking, setAssistantIsSpeaking] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [subdomainConfig, setSubdomainConfig] = useState(null);
  const [callParams, setCallParams] = useState({
    assistantId: DEFAULT_ASSISTANT_ID,
    assistantOverrides: {
      recordingEnabled: true
    }
  });

  // Track initialization attempts
  const hasInitializedRef = useRef(false);
  const hasStartedCallRef = useRef(false);

  // For development mode
  const [forceMockMode, setForceMockMode] = useState(false);

  // Define callbacks for Vapi event listeners
  const callbacks = {
    onCallStart: () => {
      console.log("Call started - setting status to CONNECTED");
      // Force status update to CONNECTED
      setStatus(CALL_STATUS.CONNECTED);

      // Emit analytics event for successful call start
      try {
        if (typeof window !== 'undefined' && window.analytics) {
          window.analytics.track('call_connected', {
            assistantId: callParams.assistantId,
            subdomain
          });
        }
      } catch (analyticsError) {
        console.warn("Failed to track analytics event:", analyticsError);
      }
    },
    onCallStarted: () => {
      // Alias for onCallStart for backward compatibility
      console.log("Call started (via onCallStarted) - setting status to CONNECTED");
      setStatus(CALL_STATUS.CONNECTED);
    },
    onCallEnd: () => {
      console.log("Call ended");
      setStatus('ended');
      if (typeof onEndCall === 'function') {
        // Check if the assistant is still speaking when the call ends
        if (assistantIsSpeaking) {
          console.log("Call ended while assistant is still speaking - passing forcedWhileSpeaking flag");
          onEndCall({
            ...dossierData,
            forcedWhileSpeaking: true
          });
        } else {
          onEndCall(dossierData);
        }
      }
    },
    onError: (error) => {
      console.error("Vapi error:", error);
      console.warn("Continuing despite Vapi error");
      // Log the error but don't change the status to error
      // This allows the application to continue even if there are errors
      setErrorMessage(`Voice assistant error: ${error.message || 'Unknown error'}`);
      // Don't set status to error, just log it
      // setStatus('error');
    },
    onSpeechStart: () => {
      console.log("Assistant started speaking");
      setAssistantIsSpeaking(true);
    },
    onSpeechEnd: () => {
      console.log("Assistant stopped speaking");
      setAssistantIsSpeaking(false);
    },
    onVolumeLevel: (level) => {
      setVolumeLevel(level);
    },
    // Add model-output handler
    onModelOutput: (output) => {
      console.log("Received model output:", output);
      if (output && output.text) {
        const timestamp = new Date().toISOString();

        // Add to message history if not a duplicate
        setMessageHistory(prev => {
          // Check for duplicates
          const isDuplicate = prev.some(msg =>
            msg.role === 'assistant' &&
            msg.content === output.text
          );

          if (isDuplicate) {
            console.log('Skipping duplicate model output:', output.text);
            return prev;
          }

          console.log('Adding model output to message history:', output.text);
          return [...prev, {
            role: 'assistant',
            content: output.text,
            timestamp,
            id: `model-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
          }];
        });
      }
    },
    // Add transcript handler
    onTranscript: (transcript) => {
      console.log("Received transcript:", transcript);
      if (transcript && (transcript.transcript || transcript.text)) {
        const transcriptText = transcript.transcript || transcript.text;

        // Only add final transcripts
        if (transcript.is_final || transcript.transcriptType === 'final') {
          const timestamp = new Date().toISOString();

          // Add to message history if not a duplicate
          setMessageHistory(prev => {
            // Check for duplicates
            const isDuplicate = prev.some(msg =>
              msg.role === 'user' &&
              msg.content === transcriptText
            );

            if (isDuplicate) {
              console.log('Skipping duplicate transcript:', transcriptText);
              return prev;
            }

            console.log('Adding transcript to message history:', transcriptText);
            return [...prev, {
              role: 'user',
              content: transcriptText,
              timestamp,
              isTranscript: true,
              id: `transcript-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
            }];
          });
        }
      }
    },
    // Add transcription handler (alias for transcript)
    onTranscription: (transcript) => {
      console.log("Received transcription:", transcript);
      if (transcript && (transcript.transcript || transcript.text)) {
        const transcriptText = transcript.transcript || transcript.text;

        // Only add final transcripts
        if (transcript.is_final || transcript.transcriptType === 'final') {
          const timestamp = new Date().toISOString();

          // Add to message history if not a duplicate
          setMessageHistory(prev => {
            // Check for duplicates
            const isDuplicate = prev.some(msg =>
              msg.role === 'user' &&
              msg.content === transcriptText
            );

            if (isDuplicate) {
              console.log('Skipping duplicate transcription:', transcriptText);
              return prev;
            }

            console.log('Adding transcription to message history:', transcriptText);
            return [...prev, {
              role: 'user',
              content: transcriptText,
              timestamp,
              isTranscript: true,
              id: `transcription-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
            }];
          });
        }
      }
    },
    onMessage: (message) => {
      console.log("Received message:", message);

      if (message.type === 'conversation-update') {
        const messages = message.messages || [];
        // Filter out the default first message if we have a custom one
        const filteredMessages = messages.filter(msg => {
          // If this is the first message from the assistant and we have a custom welcome message,
          // filter it out to prevent the default message from being shown
          if (msg.role === 'bot' &&
              msg.secondsFromStart < 2 &&
              customInstructions &&
              (customInstructions.initialMessage || customInstructions.firstMessage ||
               customInstructions.assistantOverrides?.firstMessage)) {
            console.log('Filtering out default first message:', msg.message);
            return false;
          }
          return true;
        });

        // Process each message with enhanced duplicate detection
        filteredMessages.forEach((msg) => {
          const timestamp = new Date().toISOString();
          const role = msg.role === 'bot' ? 'assistant' : 'user';
          const content = msg.message;
          const uniqueId = `conv-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

          // Skip system messages - they should never be added to the message history for display
          if (role === 'system' || msg.role === 'system') {
            console.log('Skipping system message, not for display:', content.substring(0, 50) + '...');
            return;
          }

          // Check if the message looks like a system prompt (contains specific keywords)
          const looksLikeSystemPrompt = (text) => {
            const systemPromptIndicators = [
              'You are a legal assistant',
              'You are an AI assistant',
              'You have access to a powerful web search tool',
              'When to use the Firecrawl search tool',
              'How to use the firecrawl_search tool',
              'You should always identify yourself as "Scout"',
              'Your name is Scout',
              'You are Scout',
              'You are a helpful assistant'
            ];

            const textLower = text.toLowerCase();
            return systemPromptIndicators.some(indicator =>
              textLower.includes(indicator.toLowerCase())
            );
          };

          // Skip messages that look like system prompts
          if (looksLikeSystemPrompt(content)) {
            console.log('Skipping message that looks like a system prompt:', content.substring(0, 50) + '...');
            return;
          }

          // Check for duplicates before adding to message history
          setMessageHistory(prev => {
            // More aggressive duplicate detection
            const isDuplicate = prev.some(existingMsg => {
              // Exact match
              if (existingMsg.role === role && existingMsg.content === content) {
                return true;
              }

              // For user messages, also check for similar content
              if (role === 'user' && existingMsg.role === 'user' &&
                  existingMsg.content && content) {
                const contentLower = content.toLowerCase();
                const existingLower = existingMsg.content.toLowerCase();

                if (contentLower.includes(existingLower) || existingLower.includes(contentLower)) {
                  console.log('Found similar existing message:', {
                    existing: existingLower,
                    new: contentLower
                  });
                  return true;
                }
              }

              return false;
            });

            if (isDuplicate) {
              console.log(`Skipping duplicate ${role} message:`, content);
              return prev;
            }

            // Additional check for very long messages that might be system prompts
            if (content.length > 500 && role === 'assistant') {
              console.log('Message is suspiciously long, checking if it might be a system prompt...');

              // If it contains multiple newlines and looks like instructions, skip it
              if (content.split('\n').length > 5 && looksLikeSystemPrompt(content)) {
                console.log('Skipping long message that appears to be a system prompt');
                return prev;
              }
            }

            console.log(`Adding new ${role} message to history:`, content);
            return [...prev, {
              role,
              content,
              timestamp,
              id: uniqueId
            }];
          });
        });
      } else if (message.type === 'transcript') {
        // Handle all transcript types, not just final ones
        console.log('Received transcript message:', message);

        // Only add final transcripts to the message history
        if (message.transcriptType === 'final' || message.is_final) {
          const timestamp = new Date().toISOString();
          const transcriptText = message.transcript || message.text;

          // Determine the role based on the message
          // Only add user transcripts, not assistant transcripts
          const role = message.role === 'assistant' ? 'assistant' : 'user';

          // Skip assistant transcripts - they will come through model-output
          if (role === 'user') {
            console.log('Processing user transcript:', transcriptText);

            // More aggressive duplicate detection for transcripts
            setMessageHistory(prev => {
              // Check if this exact transcript already exists in the last several messages
              // or if a very similar transcript exists (to catch minor variations)
              const isDuplicate = prev.some(msg => {
                // Exact match
                if (msg.role === 'user' && msg.content === transcriptText) {
                  return true;
                }

                // Similar match (if transcript is very similar to an existing one)
                if (msg.role === 'user' && msg.content && transcriptText) {
                  // Calculate similarity (basic version - check if one contains the other)
                  const contentLower = msg.content.toLowerCase();
                  const transcriptLower = transcriptText.toLowerCase();

                  if (contentLower.includes(transcriptLower) || transcriptLower.includes(contentLower)) {
                    console.log('Found similar existing transcript:', {
                      existing: contentLower,
                      new: transcriptLower
                    });
                    return true;
                  }
                }

                return false;
              });

              if (isDuplicate) {
                console.log('Skipping duplicate or similar user transcript:', transcriptText);
                return prev;
              }

              // Add a unique ID to help with deduplication
              const uniqueId = `transcript-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
              console.log('Adding new user transcript to message history with ID:', uniqueId);

              return [...prev, {
                role: 'user',
                content: transcriptText,
                timestamp,
                id: uniqueId,
                isTranscript: true
              }];
            });
          } else {
            console.log('Skipping assistant transcript, will be handled by model-output:', transcriptText);
          }
        }
      } else if (message.type === 'add-message-response') {
        const timestamp = new Date().toISOString();
        const content = message.message.content;

        // Check for duplicates before adding to message history
        setMessageHistory(prev => {
          // Check if this exact message already exists in the last few messages
          const isDuplicate = prev.slice(-5).some(existingMsg =>
            existingMsg.role === 'assistant' &&
            existingMsg.content === content
          );

          if (isDuplicate) {
            console.log('Skipping duplicate add-message-response:', content);
            return prev;
          }

          console.log('Adding new add-message-response to history:', content);
          return [...prev, {
            role: 'assistant',
            content,
            timestamp,
            id: Date.now() + Math.random()
          }];
        });
      } else if (message.type === 'model-output') {
        // Handle model output text content
        if (message.output && message.output.content) {
          console.log('Received model output with content:', message.output.content);
          const timestamp = new Date().toISOString();

          setMessageHistory(prev => {
            // Check if this exact message content already exists in the last few messages
            const isDuplicate = prev.slice(-5).some(msg =>
              msg.role === 'assistant' && msg.content === message.output.content
            );

            // Only add if not a duplicate
            if (!isDuplicate) {
              console.log('Adding new assistant message to history:', message.output.content);
              return [...prev, {
                role: 'assistant',
                content: message.output.content,
                timestamp,
                id: Date.now() + Math.random()
              }];
            }
            console.log('Skipping duplicate assistant message:', message.output.content);
            return prev;
          });
        }

        // Process tool calls if present
        if (Array.isArray(message.output)) {
          // Process each tool call in the output array
          message.output.forEach(toolCall => {
            if (toolCall.function && toolCall.function.name === 'live_dossier') {
              processDossierToolCall(toolCall);
            }
          });
        }
      }

      // Also keep the existing tool_call processing for backward compatibility
      if (message.tool_call && message.tool_call.id === VAPI_TOOL_IDS.LIVE_DOSSIER) {
        processDossierToolCall(message.tool_call);
      }

      // Process location data from regular messages
      if (message.content) {
        try {
          const locationData = vapiService.extractLocationData(message.content);
          if (locationData && locationData.hasLocation) {
            updateDossierData({ location: locationData.location });
          }
        } catch (e) {
          console.error("Error extracting location data:", e);
        }
      }
    }
  };

  // Import web search system prompt
  const getWebSearchSystemPrompt = () => {
    return `
You have access to a powerful web search tool called Firecrawl that can help you find information on the internet, especially for legal research.

When to use the Firecrawl search tool:
1. When asked about current events, news, or recent developments
2. When asked about specific legal cases, statutes, or regulations
3. When you need to provide factual information that may have changed since your training data
4. When asked about specific organizations, people, or places that you don't have detailed information about
5. When you need to cite legal precedents or statutes

How to use the firecrawl_search tool:
1. When a user asks a question that would benefit from web search, use the firecrawl_search tool
2. Formulate a clear, specific search query related to the user's question
3. Specify the number of results you want (usually 3 is sufficient)
4. For legal questions, set the legalFocus parameter to true
5. Choose an appropriate format for the results based on the type of information:
   - "simple" for general information (default)
   - "detailed" for comprehensive information with full content
   - "legal" for legal research with citation formatting

Example tool usage:
For a question about "recent changes to California tenant laws":
{
  "query": "California tenant law changes 2023",
  "numResults": 3,
  "legalFocus": true,
  "format": "legal"
}

After receiving search results:
1. Analyze the information from the search results
2. Synthesize a response that directly addresses the user's question
3. Cite your sources by mentioning the websites or publications you found information from
4. If the search results include legal citations, include them in your response
5. If the search results don't provide adequate information, acknowledge this and provide the best answer you can based on your training

Remember to maintain a professional tone and clarify that you're providing information, not legal advice.
`;
  };

  // Load subdomain configuration
  useEffect(() => {
    // Skip if we've already loaded the config and have a valid vapi instance
    if (subdomainConfig && vapi && hasStartedCallRef.current) {
      return;
    }

    const loadSubdomainConfig = async () => {
      try {
        // If explicit assistantId is provided without assistantOverrides, use the assistant without any overrides
        if (assistantId && !assistantOverrides) {
          debug.log('Using explicit assistantId without any overrides (including voice):', assistantId);
          console.log('useVapiCall: Using explicit assistantId without overrides:', assistantId);

          // Create a minimal config object
          const config = {
            firmName: customInstructions?.firmName || 'LegalScout',
            vapiInstructions: customInstructions?.vapiInstructions || '',
            vapiContext: customInstructions?.vapiContext || ''
          };

          setSubdomainConfig(config);

          // For all assistants, use the standard approach with no overrides
          setCallParams(prev => {
            console.log("Setting up call params with explicit assistantId and no overrides at all:", assistantId);

            return {
              ...prev,
              assistantId: assistantId,
              // No assistantOverrides at all - use the assistant's default configuration completely
              assistantOverrides: null,
              // Clear any other parameters that might override the default assistant
              initialMessage: null,
              welcomeMessage: null,
              instructions: null,
              systemPrompt: null
            };
          });
        }
        // If assistantOverrides are provided directly, use them
        else if (assistantOverrides) {
          debug.log('Using provided assistantOverrides directly:', assistantOverrides);

          // Create a minimal config object
          const config = {
            firmName: customInstructions?.firmName || 'LegalScout',
            vapiInstructions: customInstructions?.vapiInstructions || '',
            vapiContext: customInstructions?.vapiContext || ''
          };

          setSubdomainConfig(config);

          // Update call parameters with the direct assistantOverrides
          setCallParams(prev => {
            // Use explicit assistantId if provided, otherwise use from customInstructions or default
            const effectiveAssistantId = assistantId || customInstructions?.assistantId || DEFAULT_ASSISTANT_ID;

            console.log("Setting up call params with direct assistantOverrides and assistantId:", effectiveAssistantId);

            // Log the assistantOverrides for debugging
            console.log('Using direct assistantOverrides:', {
              firstMessage: assistantOverrides.firstMessage ? 'Present' : 'Not present',
              systemPrompt: assistantOverrides.systemPrompt ? 'Present' : 'Not present',
              systemPromptLength: assistantOverrides.systemPrompt ? assistantOverrides.systemPrompt.length : 0
            });

            // Note: systemPrompt is not supported by Vapi API
            // We'll store it separately for reference but not include it in assistantOverrides
            const systemPrompt = (assistantOverrides.systemPrompt || '') + '\n\n' + getWebSearchSystemPrompt();

            // Create a new assistantOverrides object without systemPrompt
            const { systemPrompt: _, ...cleanedAssistantOverrides } = assistantOverrides;

            return {
              ...prev,
              assistantId: effectiveAssistantId,
              instructions: customInstructions?.vapiInstructions || '',
              initialMessage: assistantOverrides.firstMessage || `Hi, I'm Scout from ${config.firmName}. How can I help you today?`,
              systemPrompt, // Store separately
              assistantOverrides: {
                ...cleanedAssistantOverrides,
                recordingEnabled: true,
                voice: {
                  provider: customInstructions?.voiceProvider || 'openai',
                  voiceId: customInstructions?.voiceId || 'echo'
                }
              }
            };
          });
        }
        // If customInstructions are provided, use them instead of loading from subdomain
        else if (customInstructions) {
          debug.log('Using provided customInstructions:', customInstructions);

          // Create a config object from customInstructions
          const config = {
            firmName: customInstructions.firmName || 'LegalScout',
            vapiInstructions: customInstructions.vapiInstructions || '',
            vapiContext: customInstructions.vapiContext || ''
          };

          setSubdomainConfig(config);

          // Update call parameters based on custom instructions
          setCallParams(prev => {
            // Use explicit assistantId if provided, otherwise use from customInstructions or default
            const effectiveAssistantId = assistantId || customInstructions.assistantId || DEFAULT_ASSISTANT_ID;

            // Get initial message from customInstructions or create default
            const initialMessage = customInstructions.initialMessage ||
              customInstructions.welcomeMessage ||
              `Hi, I'm Scout from ${config.firmName}. How can I help you today?`;

            console.log('Setting initial message:', initialMessage);

            console.log("Setting up call params with custom instructions and assistantId:", effectiveAssistantId);

            // Debug log for assistantOverrides
            if (customInstructions.assistantOverrides) {
              console.log("Using assistantOverrides:", {
                firstMessage: customInstructions.assistantOverrides.firstMessage ? 'Present' : 'Not present',
                initialMessage: customInstructions.assistantOverrides.initialMessage ? 'Present' : 'Not present',
                systemPrompt: customInstructions.assistantOverrides.systemPrompt ? 'Present' : 'Not present',
                systemPromptLength: customInstructions.assistantOverrides.systemPrompt ? customInstructions.assistantOverrides.systemPrompt.length : 0
              });
            }

            // Extract direct Vapi API parameters from customInstructions
            const directApiParams = {};

            // Check for direct API parameters at the root level
            if (customInstructions.firstMessage) {
              directApiParams.firstMessage = customInstructions.firstMessage;
            }

            if (customInstructions.systemPrompt) {
              directApiParams.systemPrompt = customInstructions.systemPrompt;
            }

            console.log('Direct API parameters found:', directApiParams);

            // Create a new assistantOverrides object with all the parameters
            const newAssistantOverrides = {
              recordingEnabled: true,
              context: customInstructions.vapiContext || '',
              firstMessage: customInstructions.welcomeMessage || customInstructions.initialMessage,
              firstMessageMode: 'assistant-speaks-first',
              ...(customInstructions.assistantOverrides || {}),
              ...directApiParams // Add direct API parameters
            };

            // Add web search instructions to system prompt
            let systemPrompt = newAssistantOverrides.systemPrompt || '';
            systemPrompt += '\n\n' + getWebSearchSystemPrompt();
            newAssistantOverrides.systemPrompt = systemPrompt;

            // Log the final assistantOverrides for debugging
            console.log('Final assistantOverrides:', {
              ...newAssistantOverrides,
              systemPrompt: newAssistantOverrides.systemPrompt ? 'Present (length: ' + newAssistantOverrides.systemPrompt.length + ')' : 'Not present',
              firstMessage: newAssistantOverrides.firstMessage || 'Not present'
            });

            return {
              ...prev,
              assistantId: effectiveAssistantId,
              instructions: customInstructions.vapiInstructions || '',
              initialMessage,
              assistantOverrides: {
                ...newAssistantOverrides,
                voice: {
                  provider: customInstructions.voiceProvider || 'openai',
                  voiceId: customInstructions.voiceId || 'echo'
                }
              }
            };
          });
        } else {
          // Load config from subdomain if no customInstructions provided
          const config = await getAttorneyConfigAsync(subdomain);
          debug.log('Loaded subdomain config:', config);
          setSubdomainConfig(config);

          // Update call parameters based on subdomain config
          setCallParams(prev => {
            // Use explicit assistantId if provided, otherwise get from config
            let effectiveAssistantId = assistantId;

            // If no explicit assistantId was provided, try to get from config
            if (!effectiveAssistantId) {
              // Prioritize the vapi_assistant_id from Supabase as the single source of truth
              if (config.vapi_assistant_id) {
                console.log('[useVapiCall] Using vapi_assistant_id from Supabase:', config.vapi_assistant_id);
                effectiveAssistantId = config.vapi_assistant_id;
              }
              // Fall back to URL extraction if available
              else if (config.vapi_url) {
                const extractedId = config.vapi_url.split('/').pop();
                console.log('[useVapiCall] Extracted assistant ID from URL:', extractedId);
                effectiveAssistantId = extractedId;
              }
              // CRITICAL FIX: Use default assistant for home page calls
              else if (subdomain === 'default') {
                console.log('[useVapiCall] Using default assistant for home page');
                effectiveAssistantId = '6f2634a6-358b-48cb-ae81-ce91fa4fb1d6'; // Default LegalScout assistant
              }
              // For dashboard preview, we should not use a default assistant
              else {
                console.warn('[useVapiCall] No assistant ID found in config');
                effectiveAssistantId = null;
              }
            }

            // Validate the assistantId is not empty or undefined
            if (!effectiveAssistantId || effectiveAssistantId.trim() === '') {
              console.warn("[useVapiCall] Invalid assistant ID");
              effectiveAssistantId = null;
            }

            // Only warn about invalid format if needed
            if (effectiveAssistantId && effectiveAssistantId.length < 32) {
              console.warn("[useVapiCall] Assistant ID appears invalid, may cause errors");
            }

            console.log("Setting up call params with assistantId:", effectiveAssistantId);

            // For attorney dashboard preview, we should use the assistant without any overrides
            // This ensures we're using the assistant as configured in Vapi
            return {
              ...prev,
              assistantId: effectiveAssistantId,
              // No assistantOverrides at all - use the assistant's default configuration completely
              assistantOverrides: null,
              // Clear any other parameters that might override the default assistant
              initialMessage: null,
              welcomeMessage: null,
              instructions: null,
              systemPrompt: null
            };
          });
        }

        // Automatically start the call after config is loaded
        if (vapi && !hasStartedCallRef.current) {
          hasStartedCallRef.current = true;
          requestMicrophoneAndStartCall();
        }
      } catch (error) {
        console.error('Error loading subdomain config:', error);
      }
    };

    loadSubdomainConfig();
  }, [subdomain, vapi, customInstructions, assistantOverrides]);

  // Add effect to start call when Vapi instance is ready
  useEffect(() => {
    if (vapi && !hasStartedCallRef.current && subdomainConfig) {
      hasStartedCallRef.current = true;
      requestMicrophoneAndStartCall();
    }
  }, [vapi, subdomainConfig]);

  // Helper function to initialize the Vapi instance
  const initializeVapi = async () => {
    try {
      // Try to get API key from environment variables or window object (fallback)
      let apiKey = null;
      try {
        apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
      } catch (e) {
        // import.meta.env may not be available in all contexts
      }
      if (!apiKey) {
        apiKey = window.VITE_VAPI_PUBLIC_KEY;
      }
      // No hardcoded fallback - require proper environment configuration

      console.log('[useVapiCall] Checking for Vapi public key...');
      // Safely check environment variables without causing hook violations
      let envApiKey = null;
      try {
        envApiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
      } catch (e) {
        // import.meta.env may not be available in all contexts
      }
      console.log('[useVapiCall] VITE_VAPI_PUBLIC_KEY:', envApiKey ? 'Set' : 'Not set');
      console.log('[useVapiCall] window.VITE_VAPI_PUBLIC_KEY:', window.VITE_VAPI_PUBLIC_KEY ? 'Set' : 'Not set');
      console.log('[useVapiCall] Using API key:', apiKey ? apiKey.substring(0, 8) + '...' : 'None');

      if (!apiKey) {
        console.error('[useVapiCall] No Vapi public API key found in environment variables');
        console.error('[useVapiCall] Please set VITE_VAPI_PUBLIC_KEY in your .env file');
        setErrorMessage("Missing Vapi public API key. Please configure VITE_VAPI_PUBLIC_KEY in your environment.");
        setForceMockMode(true);
        return;
      }

      console.log("[useVapiCall] Initializing Vapi instance with API key:", apiKey.substring(0, 8) + '...');

      console.log("[useVapiCall] Creating Vapi instance directly using official pattern");

      // Create Vapi instance using ES module import
      try {
        console.log("[useVapiCall] Creating Vapi instance with API key:", apiKey.substring(0, 8) + '...');

        // Create Vapi instance directly using ES module import
        const vapiInstance = new Vapi(apiKey);

        console.log("[useVapiCall] Vapi instance created successfully");

        // Set up event listeners
        vapiService.setupEventListeners(vapiInstance, callbacks);
        setVapi(vapiInstance);
        return;
      } catch (directError) {
        console.error("[useVapiCall] Failed to create Vapi instance:", directError.message);
        throw directError;
      }
    } catch (error) {
      console.error("Error initializing Vapi:", error);
      setErrorMessage("Failed to initialize voice assistant: " + error.message);
      setStatus('error');
    }
  };

  // Initialize Vapi when the component mounts
  useEffect(() => {
    // Skip initialization if in mock mode
    if (forceMockMode) {
      console.log("Using mock mode - skipping Vapi initialization");
      return;
    }

    // Only try to initialize once
    if (hasInitializedRef.current) {
      return;
    }

    hasInitializedRef.current = true;
    initializeVapi();
  }, [subdomainConfig, forceMockMode]);

  // Separate effect to set up event listeners after Vapi is initialized
  useEffect(() => {
    if (!vapi || status === 'error' || forceMockMode) return;

    try {
      // Check if vapi has all required methods before setting up listeners
      if (typeof vapi.on !== 'function') {
        console.error("Vapi instance is missing 'on' method required for event listeners");
        setErrorMessage("Voice assistant initialization incomplete");
        setStatus('error');
        return;
      }

      // Set up event listeners
      console.log("Setting up Vapi event listeners");
      vapiService.setupEventListeners(vapi, callbacks);

      // Clean up listeners when component unmounts
    return () => {
        console.log("Component unmounting - performing Vapi cleanup");

        if (vapi) {
          try {
            // Remove event listeners
            vapiService.removeEventListeners(vapi);

            // Stop call if active
            if (status === 'connected' || status === 'active' || status === 'connecting') {
              console.log("Stopping active call during cleanup");
              vapi.stop();
            }

            // Call onEndCall if needed
            if (onEndCall && (status === 'connected' || status === 'active')) {
              console.log("Calling onEndCall callback during unmount");
              onEndCall(dossierData);
            }
          } catch (e) {
            console.warn("Error during Vapi cleanup:", e.message);
          }
        }
      };
    } catch (error) {
      console.error("Error setting up Vapi event listeners:", error);
      setErrorMessage("Error setting up voice assistant: " + error.message);
      setStatus('error');
    }
  }, [vapi, status, forceMockMode]);

  // Function to update dossier data
  const updateDossierData = useCallback((newData) => {
    console.log("Updating dossier data with:", newData);
    setDossierData(prevData => {
      // Special handling for location to avoid overwriting
      if (newData.location && prevData.location) {
        newData.location = {
          ...prevData.location,
          ...newData.location
        };
      }

      const updatedData = { ...prevData, ...newData };
      console.log("Updated dossier data:", updatedData);

      return updatedData;
    });
  }, []);

  // Helper function to process dossier tool calls
  const processDossierToolCall = (toolCall) => {
    try {
      console.log("🔍 Processing live_dossier tool call:", toolCall);

      // Get the arguments - they might be a string or an object
      const dossierUpdate = typeof toolCall.function.arguments === 'string'
        ? JSON.parse(toolCall.function.arguments)
        : toolCall.function.arguments;

      console.log("📦 Parsed dossier update:", dossierUpdate);

      // Handle both uppercase and lowercase field names for the outer structure
      const dossierJson = dossierUpdate.Dossier_json || dossierUpdate.dossier_json;
      console.log("🔄 Extracted dossier JSON:", dossierJson);

      if (!dossierJson) {
        console.error("❌ No dossier_json found in update:", dossierUpdate);
        return;
      }

      // Transform the data to match our internal structure
      const processedData = {
        status: dossierJson.STATUS || dossierJson.status || dossierJson.Status,
        clientBackground: dossierJson.CLIENT_BACKGROUND || dossierJson.client_background || dossierJson.clientBackground,
        statementOfFacts: dossierJson.STATEMENT_OF_FACTS || dossierJson.statement_of_facts || dossierJson.statementOfFacts,
        objectives: dossierJson.OBJECTIVES || dossierJson.objectives || dossierJson.Objectives,
        location: dossierJson.JURISDICTION || dossierJson.jurisdiction || dossierJson.Jurisdiction,
        legalIssues: dossierJson.LEGAL_ISSUES || dossierJson.legal_issues || dossierJson.legalIssues
      };

      console.log("🔧 Processed data before filtering:", processedData);

      // Filter out undefined and empty string values
      const filteredData = Object.fromEntries(
        Object.entries(processedData).filter(([key, value]) => {
          const keep = value !== undefined && value !== '';
          console.log(`📊 Field ${key}: ${value} - Keep? ${keep}`);
          return keep;
        })
      );

      console.log("✨ Filtered data to update:", filteredData);

      // Force a state update by creating a completely new object
      setDossierData(prev => {
        console.log("📝 Previous dossier data:", prev);
        // Create a new object with all previous data
        const updatedData = { ...prev };

        // Update each field if it exists in the filtered data
        Object.entries(filteredData).forEach(([key, value]) => {
          // Special handling for location data
          if (key === 'location' && typeof value === 'object') {
            updatedData[key] = {
              ...updatedData[key],
              ...value
            };
          } else {
            updatedData[key] = value;
          }
          console.log(`🔄 Updated ${key} to:`, updatedData[key]);
        });

        console.log("✅ Final updated dossier data:", updatedData);
        // Return a new object to ensure React detects the change
        return { ...updatedData };
      });

      // Sync with Make webhook if needed
      if (typeof vapiService.syncWithMakeWebhook === 'function') {
        vapiService.syncWithMakeWebhook(MAKE_WEBHOOK_URLS.LIVE_DOSSIER, {
          toolCallId: toolCall.id,
          existingDossier: filteredData,
          user_message: dossierUpdate.user_message,
          Scout_message: dossierUpdate.Scout_message || dossierUpdate.scout_message
        });
      }
    } catch (error) {
      console.error("❌ Error processing live_dossier update:", error);
      console.error("❌ Tool call:", toolCall);
      console.error("❌ Stack trace:", error.stack);
    }
  };

  // Helper function to start a call with a specific instance
  const startCallWithInstance = useCallback(async (instance) => {
    if (!instance) {
      console.error("Cannot start call - no Vapi instance provided");
      setStatus('error');
      setErrorMessage("No Vapi instance available");
      return;
    }

    try {
      // Use the assistantId parameter directly instead of relying on callParams state
      const effectiveAssistantId = assistantId || callParams.assistantId;

      // Validate call parameters
      if (!effectiveAssistantId) {
        console.error("Missing assistant ID. assistantId param:", assistantId, "callParams.assistantId:", callParams.assistantId);
        throw new Error("Missing assistant ID in call parameters");
      }

      // Log the call parameters being used
      console.log("Starting call with parameters:", {
        effectiveAssistantId: effectiveAssistantId.substring(0, 8) + '...',
        assistantIdParam: assistantId ? assistantId.substring(0, 8) + '...' : 'Not provided',
        callParamsAssistantId: callParams.assistantId ? callParams.assistantId.substring(0, 8) + '...' : 'Not set',
        voice: callParams.assistantOverrides?.voice || 'Not specified'
      });

      // Enhanced instance validation
      const requiredMethods = ['start', 'stop'];
      const missingMethods = requiredMethods.filter(method =>
        typeof instance[method] !== 'function'
      );

      if (missingMethods.length > 0) {
        throw new Error(`Vapi instance missing required methods: ${missingMethods.join(', ')}`);
      }

      // Use the latest Vapi Web SDK pattern - just call vapi.start(assistantId)
      console.log("[useVapiCall] Using latest Vapi Web SDK pattern");
      console.log("[useVapiCall] Calling instance.start() with assistant ID:", effectiveAssistantId);

      // Use the simple Vapi Web SDK pattern from the latest documentation
      // Bypass VapiService completely and call the Vapi instance directly
      const call = await instance.start(effectiveAssistantId);
      console.log("[useVapiCall] Direct Vapi call started successfully:", call);

      // Set status to CONNECTED
      console.log("[useVapiCall] Vapi call started successfully");
      setStatus(CALL_STATUS.CONNECTED);

      // Reset the initialization flags
      hasInitializedRef.current = true;
      hasStartedCallRef.current = true;

      // Force a status update after a short delay to ensure UI updates
      setTimeout(() => {
        console.log("Forcing status update to CONNECTED");
        setStatus(CALL_STATUS.CONNECTED);
      }, 500);

      // Emit analytics event for successful call start
      try {
        if (typeof window !== 'undefined' && window.analytics) {
          window.analytics.track('call_started_successfully', {
            assistantId: effectiveAssistantId,
            subdomain
          });
        }
      } catch (analyticsError) {
        console.warn("Failed to track analytics event:", analyticsError);
      }
    } catch (error) {
      console.error("Error in startCallWithInstance:", error);
      setStatus('error');
      setErrorMessage(`Error starting call: ${error?.message || 'An unknown error occurred'}`);
      // Reset the flags so the user can try again
      hasInitializedRef.current = false;
      hasStartedCallRef.current = false;
    }
  }, [assistantId, callParams, setStatus, setErrorMessage, subdomain]);

  // Function to request microphone permissions and start the call
  const requestMicrophoneAndStartCall = useCallback(async () => {
    try {
      console.log("Requesting microphone permission...");

      // Get available audio devices first to avoid "preferred mic not found" issue
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices.filter(device => device.kind === 'audioinput');
      console.log("Available audio input devices:", audioInputs.map(d => ({
        deviceId: d.deviceId,
        label: d.label || 'Unknown Device',
        groupId: d.groupId
      })));

      // Use the default device explicitly to avoid device selection issues
      const audioConstraints = {
        audio: {
          deviceId: audioInputs.length > 0 ? { ideal: audioInputs[0].deviceId } : undefined,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000,
          channelCount: 1
        }
      };

      console.log("Using audio constraints:", audioConstraints);

      navigator.mediaDevices.getUserMedia(audioConstraints)
        .then(async (stream) => {
          console.log("Microphone permission granted with device:", stream.getAudioTracks()[0]?.label);
          stream.getTracks().forEach(track => track.stop()); // Stop the tracks we just got

          // Ensure we have a valid Vapi instance and call parameters
          if (!vapi) {
            console.log("Vapi not available after microphone permission, initializing now...");
            // Try to initialize Vapi explicitly here if needed
            const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
            if (!apiKey) {
              throw new Error("Missing Vapi API key");
            }

            const effectiveAssistantId = assistantId || callParams.assistantId;
            if (!effectiveAssistantId) {
              throw new Error("Missing assistant ID in call parameters");
            }

            vapiService.initialize(apiKey, subdomain)
              .then(directVapiInstance => {
                if (!directVapiInstance) {
                  throw new Error("Failed to create Vapi instance");
                }

                console.log("Successfully created Vapi instance directly");
                // Set up event listeners
                vapiService.setupEventListeners(directVapiInstance, callbacks);
                // Update state
                setVapi(directVapiInstance);
                // Wait briefly for state update
                setTimeout(async () => {
                  console.log("Starting call with direct Vapi instance using direct pattern");
                  try {
                    const call = await directVapiInstance.start(effectiveAssistantId);
                    console.log("Direct Vapi call started successfully:", call);
                    setStatus(CALL_STATUS.CONNECTED);
                  } catch (error) {
                    console.error("Error starting direct call:", error);
                    setStatus('error');
                    setErrorMessage(`Failed to start call: ${error.message}`);
                  }
                }, 100);
              })
              .catch(error => {
                console.error("Error initializing Vapi directly:", error);
                setStatus('error');
                setErrorMessage("Failed to initialize Vapi: " + error.message);
              });
          } else {
            console.log("Starting call with existing Vapi instance using direct pattern");
            try {
              const effectiveAssistantId = assistantId || callParams.assistantId;
              const call = await vapi.start(effectiveAssistantId);
              console.log("Direct Vapi call started successfully:", call);
              setStatus(CALL_STATUS.CONNECTED);
            } catch (error) {
              console.error("Error starting direct call:", error);
              setStatus('error');
              setErrorMessage(`Failed to start call: ${error.message}`);
            }
          }
        })
        .catch(err => {
          console.error("Microphone permission denied:", err);
          setStatus('error');
          setErrorMessage("Microphone permission denied. Please enable microphone access and try again.");
        });
    } catch (error) {
      console.error("Error requesting microphone permission:", error);
      setStatus('error');
      setErrorMessage("Error requesting microphone permission: " + error.message);
    }
  }, [assistantId, vapi, subdomain, callbacks, callParams, setStatus, setErrorMessage]);

  // Function to start the call - simplified to follow official Vapi Web SDK pattern
  const startCall = useCallback(async () => {
    try {
      setStatus('connecting');
      setErrorMessage(null);

      const effectiveAssistantId = assistantId || callParams.assistantId;

      if (!effectiveAssistantId) {
        console.error("Missing assistant ID for call");
        setStatus('error');
        setErrorMessage("Missing assistant ID for call");
        return;
      }

      console.log('[useVapiCall] Starting call with assistant:', effectiveAssistantId);

      // Use existing Vapi instance or create one if needed
      if (!vapi) {
        console.error('[useVapiCall] No Vapi instance available');
        setStatus('error');
        setErrorMessage("Voice assistant not initialized");
        return;
      }

      // Use the simple Vapi Web SDK pattern: vapi.start(assistantId)
      // NO OVERRIDES - this prevents the "Meeting has ended" ejection error
      console.log('[useVapiCall] Starting call using simple Vapi Web SDK pattern (no overrides)');
      const call = await vapi.start(effectiveAssistantId);
      console.log('[useVapiCall] Call started successfully:', call);

      setStatus('connected');
    } catch (error) {
      console.error('[useVapiCall] Error starting call:', error);
      setStatus('error');
      setErrorMessage(`Failed to start call: ${error.message}`);
    }
  }, [assistantId, vapi, callParams]);

  // Function to stop the call
  const stopCall = useCallback(async () => {
    try {
      console.log('[useVapiCall] Stopping call...');

      // Set status immediately
      setStatus('idle');

      // Stop the call using the service
      if (vapi) {
        await vapiService.stopCall();
      }

      // Clear the Vapi instance
      setVapi(null);

      // Reset initialization flags
      hasInitializedRef.current = false;
      hasStartedCallRef.current = false;

      // Call onEndCall callback if provided
      if (onEndCall) {
        console.log('[useVapiCall] Calling onEndCall callback');
        if (assistantIsSpeaking) {
          onEndCall({
            ...dossierData,
            forcedWhileSpeaking: true
          });
        } else {
          onEndCall(dossierData);
        }
      }

      console.log('[useVapiCall] Call stopped successfully');
    } catch (error) {
      console.error('[useVapiCall] Error stopping call:', error);
      // Still clean up even if there's an error
      setVapi(null);
      setStatus('idle');
    }
  }, [vapi, dossierData, onEndCall, assistantIsSpeaking]);

  // Pre-initialize audio devices to avoid "preferred mic not found" issues
  const preInitializeAudioDevices = useCallback(async () => {
    try {
      console.log("[useVapiCall] Pre-initializing audio devices...");

      // Enumerate devices first
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices.filter(device => device.kind === 'audioinput');

      console.log("[useVapiCall] Found audio input devices:", audioInputs.length);

      if (audioInputs.length === 0) {
        console.warn("[useVapiCall] No audio input devices found");
        return false;
      }

      // Test the default microphone briefly
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          deviceId: audioInputs[0].deviceId,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      console.log("[useVapiCall] Audio device test successful:", stream.getAudioTracks()[0]?.label);
      stream.getTracks().forEach(track => track.stop());

      return true;
    } catch (error) {
      console.error("[useVapiCall] Audio device pre-initialization failed:", error);
      return false;
    }
  }, []);

  // Add effect to auto-start call when Vapi is ready
  useEffect(() => {
    // Only attempt to start if we have both vapi instance and subdomain config
    if (vapi && subdomainConfig && !hasStartedCallRef.current) {
      console.log("Auto-starting call with existing flow");
      hasStartedCallRef.current = true;

      // Pre-initialize audio devices before starting call
      preInitializeAudioDevices().then((audioReady) => {
        if (audioReady) {
          console.log("[useVapiCall] Audio devices ready, starting call");
          startCall();
        } else {
          console.error("[useVapiCall] Audio devices not ready, cannot start call");
          setStatus('error');
          setErrorMessage("Microphone not available. Please check your audio device settings.");
        }
      });
    }
  }, [vapi, subdomainConfig, startCall, preInitializeAudioDevices]);

  // Add keyboard shortcut for developers to toggle mock mode (Shift+Alt+M)
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Only enable in development mode - safely check environment
      let isDevelopment = false;
      try {
        isDevelopment = import.meta.env.MODE === 'development';
      } catch (e) {
        // Fallback to checking hostname for development
        isDevelopment = typeof window !== 'undefined' &&
                       (window.location.hostname === 'localhost' ||
                        window.location.hostname === '127.0.0.1');
      }

      if (isDevelopment) {
        // Check for Shift+Alt+M key combination
        if (e.shiftKey && e.altKey && e.key === 'M') {
          const newMode = !forceMockMode;
          console.log(`[DEV] Keyboard shortcut: Switching to ${newMode ? 'mock' : 'real'} mode`);
          setForceMockMode(newMode);

          // Reset the call if necessary
          if (status === 'connected' || status === 'connecting') {
            stopCall();
          }

          setStatus('idle');
          // Small delay to ensure state is updated
          setTimeout(() => startCall(), 500);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [forceMockMode, status, stopCall, startCall]);

  // Return the state and functions
  return {
    vapi,
    status,
    dossierData,
    messageHistory,
    volumeLevel,
    assistantIsSpeaking,
    errorMessage,
    subdomainConfig,
    forceMockMode,
    startCall,
    stopCall,
    updateDossierData,
    callId: vapi?.callId || null // Expose the callId for emissions monitoring
  };
};

export default useVapiCall;