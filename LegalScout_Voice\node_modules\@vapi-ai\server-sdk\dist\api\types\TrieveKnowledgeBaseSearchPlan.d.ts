/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface TrieveKnowledgeBaseSearchPlan {
    /** Specifies the number of top chunks to return. This corresponds to the `page_size` parameter in Trieve. */
    topK?: number;
    /** If true, stop words (specified in server/src/stop-words.txt in the git repo) will be removed. This will preserve queries that are entirely stop words. */
    removeStopWords?: boolean;
    /** This is the score threshold to filter out chunks with a score below the threshold for cosine distance metric. For Manhattan Distance, Euclidean Distance, and Dot Product, it will filter out scores above the threshold distance. This threshold applies before weight and bias modifications. If not specified, this defaults to no threshold. A threshold of 0 will default to no threshold. */
    scoreThreshold?: number;
    /** This is the search method used when searching for relevant chunks from the vector store. */
    searchType: Vapi.TrieveKnowledgeBaseSearchPlanSearchType;
}
