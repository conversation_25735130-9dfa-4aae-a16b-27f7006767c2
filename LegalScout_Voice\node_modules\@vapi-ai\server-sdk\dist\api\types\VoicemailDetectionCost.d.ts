/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface VoicemailDetectionCost {
    /** This is the type of cost, always 'voicemail-detection' for this class. */
    type: "voicemail-detection";
    /** This is the model that was used to perform the analysis. */
    model: Record<string, unknown>;
    /** This is the provider that was used to detect the voicemail. */
    provider: Vapi.VoicemailDetectionCostProvider;
    /** This is the number of prompt text tokens used in the voicemail detection. */
    promptTextTokens: number;
    /** This is the number of prompt audio tokens used in the voicemail detection. */
    promptAudioTokens: number;
    /** This is the number of completion text tokens used in the voicemail detection. */
    completionTextTokens: number;
    /** This is the number of completion audio tokens used in the voicemail detection. */
    completionAudioTokens: number;
    /** This is the cost of the component in USD. */
    cost: number;
}
