/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface UpdateTokenDto {
    /** This is the tag for the token. It represents its scope. */
    tag?: Vapi.UpdateTokenDtoTag;
    /** This is the name of the token. This is just for your own reference. */
    name?: string;
    /** This are the restrictions for the token. */
    restrictions?: Vapi.TokenRestrictions;
}
