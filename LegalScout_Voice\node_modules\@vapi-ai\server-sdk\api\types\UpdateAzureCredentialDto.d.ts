/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface UpdateAzureCredentialDto {
    /** This is the service being used in Azure. */
    service?: Vapi.UpdateAzureCredentialDtoService;
    /** This is the region of the Azure resource. */
    region?: Vapi.UpdateAzureCredentialDtoRegion;
    /** This is not returned in the API. */
    apiKey?: string;
    /** This is the name of credential. This is just for your reference. */
    name?: string;
    /** This is the bucket plan that can be provided to store call artifacts in Azure Blob Storage. */
    bucketPlan?: Vapi.AzureBlobStorageBucketPlan;
}
