/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface ToolCallResultMessage {
    /** The role of the tool call result in the conversation. */
    role: string;
    /** The ID of the tool call. */
    toolCallId: string;
    /** The name of the tool that returned the result. */
    name: string;
    /** The result of the tool call in JSON format. */
    result: string;
    /** The timestamp when the message was sent. */
    time: number;
    /** The number of seconds from the start of the conversation. */
    secondsFromStart: number;
}
