/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the language code (ISO 639-1) that will be used.
 *
 * @default 'en-US'
 */
export type VapiVoiceLanguage = "en-US" | "en-GB" | "en-AU" | "en-CA" | "ja" | "zh" | "de" | "hi" | "fr-FR" | "fr-CA" | "ko" | "pt-BR" | "pt-PT" | "it" | "es-ES" | "es-MX" | "id" | "nl" | "tr" | "fil" | "pl" | "sv" | "bg" | "ro" | "ar-SA" | "ar-AE" | "cs" | "el" | "fi" | "hr" | "ms" | "sk" | "da" | "ta" | "uk" | "ru" | "hu" | "no" | "vi";
export declare const VapiVoiceLanguage: {
    readonly EnUs: "en-US";
    readonly EnGb: "en-GB";
    readonly EnAu: "en-AU";
    readonly EnCa: "en-CA";
    readonly Ja: "ja";
    readonly Zh: "zh";
    readonly De: "de";
    readonly Hi: "hi";
    readonly FrFr: "fr-FR";
    readonly FrCa: "fr-CA";
    readonly Ko: "ko";
    readonly PtBr: "pt-BR";
    readonly PtPt: "pt-PT";
    readonly It: "it";
    readonly EsEs: "es-ES";
    readonly EsMx: "es-MX";
    readonly Id: "id";
    readonly Nl: "nl";
    readonly Tr: "tr";
    readonly Fil: "fil";
    readonly Pl: "pl";
    readonly Sv: "sv";
    readonly Bg: "bg";
    readonly Ro: "ro";
    readonly ArSa: "ar-SA";
    readonly ArAe: "ar-AE";
    readonly Cs: "cs";
    readonly El: "el";
    readonly Fi: "fi";
    readonly Hr: "hr";
    readonly Ms: "ms";
    readonly Sk: "sk";
    readonly Da: "da";
    readonly Ta: "ta";
    readonly Uk: "uk";
    readonly Ru: "ru";
    readonly Hu: "hu";
    readonly No: "no";
    readonly Vi: "vi";
};
