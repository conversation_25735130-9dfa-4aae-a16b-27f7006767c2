/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface TranscriptionEndpointingPlan {
    /**
     * The minimum number of seconds to wait after transcription ending with punctuation before sending a request to the model. Defaults to 0.1.
     *
     * This setting exists because the transcriber punctuates the transcription when it's more confident that customer has completed a thought.
     *
     * @default 0.1
     */
    onPunctuationSeconds?: number;
    /**
     * The minimum number of seconds to wait after transcription ending without punctuation before sending a request to the model. Defaults to 1.5.
     *
     * This setting exists to catch the cases where the transcriber was not confident enough to punctuate the transcription, but the customer is done and has been silent for a long time.
     *
     * @default 1.5
     */
    onNoPunctuationSeconds?: number;
    /**
     * The minimum number of seconds to wait after transcription ending with a number before sending a request to the model. Defaults to 0.4.
     *
     * This setting exists because the transcriber will sometimes punctuate the transcription ending with a number, even though the customer hasn't uttered the full number. This happens commonly for long numbers when the customer reads the number in chunks.
     *
     * @default 0.5
     */
    onNumberSeconds?: number;
}
