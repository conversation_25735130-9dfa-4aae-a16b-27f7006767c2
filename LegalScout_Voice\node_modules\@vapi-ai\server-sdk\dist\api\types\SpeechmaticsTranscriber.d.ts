/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface SpeechmaticsTranscriber {
    /** This is the transcription provider that will be used. */
    provider: "speechmatics";
    /** This is the model that will be used for the transcription. */
    model?: "default";
    language?: Vapi.SpeechmaticsTranscriberLanguage;
    /** This is the plan for voice provider fallbacks in the event that the primary voice provider fails. */
    fallbackPlan?: Vapi.FallbackTranscriberPlan;
}
