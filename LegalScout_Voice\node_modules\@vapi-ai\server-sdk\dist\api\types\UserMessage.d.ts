/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface UserMessage {
    /** The role of the user in the conversation. */
    role: string;
    /** The message content from the user. */
    message: string;
    /** The timestamp when the message was sent. */
    time: number;
    /** The timestamp when the message ended. */
    endTime: number;
    /** The number of seconds from the start of the conversation. */
    secondsFromStart: number;
    /** The duration of the message in seconds. */
    duration?: number;
}
