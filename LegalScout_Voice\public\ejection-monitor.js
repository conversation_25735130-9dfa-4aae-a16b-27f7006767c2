/**
 * LegalScout Voice Assistant - Ejection Error Monitor
 * 
 * This script can be injected into any page to monitor for the 
 * "Meeting ended due to ejection: Meeting has ended" error
 * 
 * Usage: Add this script to your page or run in browser console
 */

(function() {
    'use strict';
    
    console.log('🔍 LegalScout Ejection Monitor Started');
    
    const ejectionData = {
        errors: [],
        events: [],
        startTime: Date.now(),
        callAttempts: 0,
        ejections: 0
    };
    
    // Monitor console errors
    const originalError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        const timestamp = new Date().toISOString();
        
        if (message.includes('ejection') || message.includes('Meeting has ended')) {
            ejectionData.ejections++;
            ejectionData.errors.push({
                timestamp,
                message,
                stack: new Error().stack,
                type: 'console.error'
            });
            
            console.log('🚨 EJECTION ERROR DETECTED:', message);
            
            // Try to get more context
            setTimeout(() => {
                console.log('📊 Ejection Context:', {
                    totalAttempts: ejectionData.callAttempts,
                    totalEjections: ejectionData.ejections,
                    errorRate: (ejectionData.ejections / ejectionData.callAttempts * 100).toFixed(1) + '%',
                    recentErrors: ejectionData.errors.slice(-3)
                });
            }, 100);
        }
        
        return originalError.apply(console, args);
    };
    
    // Monitor window errors
    window.addEventListener('error', (event) => {
        if (event.message && (event.message.includes('ejection') || event.message.includes('Meeting has ended'))) {
            ejectionData.errors.push({
                timestamp: new Date().toISOString(),
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                type: 'window.error'
            });
            
            console.log('🚨 WINDOW EJECTION ERROR:', event.message);
        }
    });
    
    // Monitor unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
        if (event.reason && event.reason.toString().includes('ejection')) {
            ejectionData.errors.push({
                timestamp: new Date().toISOString(),
                message: event.reason.toString(),
                type: 'unhandled-rejection'
            });
            
            console.log('🚨 PROMISE EJECTION ERROR:', event.reason);
        }
    });
    
    // Monitor Vapi instances if available
    function monitorVapiInstance(vapi) {
        if (!vapi || typeof vapi.on !== 'function') return;
        
        console.log('👀 Monitoring Vapi instance for ejection patterns');
        
        vapi.on('error', (error) => {
            ejectionData.events.push({
                timestamp: new Date().toISOString(),
                event: 'vapi-error',
                data: error
            });
            
            if (error && (error.message?.includes('ejection') || error.toString().includes('ejection'))) {
                console.log('🚨 VAPI EJECTION ERROR:', error);
            }
        });
        
        vapi.on('call-start', () => {
            ejectionData.callAttempts++;
            ejectionData.events.push({
                timestamp: new Date().toISOString(),
                event: 'call-start',
                attempt: ejectionData.callAttempts
            });
            console.log('📞 Call started (attempt #' + ejectionData.callAttempts + ')');
        });
        
        vapi.on('call-end', (data) => {
            ejectionData.events.push({
                timestamp: new Date().toISOString(),
                event: 'call-end',
                data: data
            });
            
            if (data && (data.reason?.includes('ejection') || data.toString().includes('ejection'))) {
                console.log('🚨 CALL ENDED DUE TO EJECTION:', data);
            }
        });
    }
    
    // Auto-detect Vapi instances
    const originalVapi = window.Vapi;
    if (originalVapi) {
        window.Vapi = function(...args) {
            const instance = new originalVapi(...args);
            monitorVapiInstance(instance);
            return instance;
        };
        
        // Copy static properties
        Object.setPrototypeOf(window.Vapi, originalVapi);
        Object.assign(window.Vapi, originalVapi);
    }
    
    // Provide manual monitoring function
    window.monitorVapiForEjection = monitorVapiInstance;
    
    // Provide data access
    window.getEjectionData = () => ({
        ...ejectionData,
        runtime: Date.now() - ejectionData.startTime,
        summary: {
            totalAttempts: ejectionData.callAttempts,
            totalEjections: ejectionData.ejections,
            errorRate: ejectionData.callAttempts > 0 ? 
                (ejectionData.ejections / ejectionData.callAttempts * 100).toFixed(1) + '%' : '0%',
            avgTimeToEjection: ejectionData.errors.length > 0 ? 
                ejectionData.errors.reduce((sum, err) => sum + (new Date(err.timestamp) - ejectionData.startTime), 0) / ejectionData.errors.length : 0
        }
    });
    
    // Provide analysis function
    window.analyzeEjectionPattern = () => {
        const data = window.getEjectionData();
        
        console.log('📊 EJECTION ANALYSIS REPORT');
        console.log('============================');
        console.log('Runtime:', (data.runtime / 1000).toFixed(1) + 's');
        console.log('Call Attempts:', data.totalAttempts);
        console.log('Ejections:', data.totalEjections);
        console.log('Error Rate:', data.summary.errorRate);
        
        if (data.errors.length > 0) {
            console.log('\n🚨 EJECTION ERRORS:');
            data.errors.forEach((error, index) => {
                console.log(`${index + 1}. [${error.timestamp}] ${error.message}`);
            });
            
            // Pattern analysis
            const errorMessages = data.errors.map(e => e.message);
            const uniqueMessages = [...new Set(errorMessages)];
            
            console.log('\n🔍 ERROR PATTERNS:');
            uniqueMessages.forEach(msg => {
                const count = errorMessages.filter(m => m === msg).length;
                console.log(`- "${msg}" (${count} times)`);
            });
        }
        
        if (data.events.length > 0) {
            console.log('\n📅 RECENT EVENTS:');
            data.events.slice(-10).forEach(event => {
                console.log(`- [${event.timestamp}] ${event.event}:`, event.data || '');
            });
        }
        
        // Recommendations
        console.log('\n💡 RECOMMENDATIONS:');
        if (data.summary.errorRate === '100%') {
            console.log('- All calls are being ejected - check assistant configuration');
            console.log('- Verify API key permissions');
            console.log('- Check for conflicting assistant overrides');
        } else if (parseFloat(data.summary.errorRate) > 50) {
            console.log('- High ejection rate - check for intermittent configuration issues');
            console.log('- Monitor network connectivity');
        } else if (data.totalEjections > 0) {
            console.log('- Occasional ejections detected - monitor for patterns');
        } else {
            console.log('- No ejections detected - system appears stable');
        }
        
        return data;
    };
    
    // Auto-analyze after 30 seconds
    setTimeout(() => {
        if (ejectionData.callAttempts > 0 || ejectionData.errors.length > 0) {
            console.log('🔍 Auto-analyzing ejection patterns...');
            window.analyzeEjectionPattern();
        }
    }, 30000);
    
    console.log('✅ Ejection monitor ready. Use window.analyzeEjectionPattern() for analysis.');
    
})();
