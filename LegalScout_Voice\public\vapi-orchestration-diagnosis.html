<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Orchestration Diagnosis</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #2980b9; }
        button:disabled { background: #bdc3c7; cursor: not-allowed; }
        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .details {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Vapi Orchestration Diagnosis Tool</h1>
        
        <div class="test-section">
            <h3>🎯 Quick Test Suite</h3>
            <button onclick="runFullDiagnosis()">Run Complete Diagnosis</button>
            <button onclick="testApiKey()">Test API Key Only</button>
            <button onclick="testWebSDK()">Test Web SDK Only</button>
            <button onclick="testCallOrchestration()">Test Call Orchestration</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>

        <div class="grid">
            <div class="test-section">
                <h3>📊 Test Results</h3>
                <div id="results"></div>
            </div>
            
            <div class="test-section">
                <h3>🔧 Environment Info</h3>
                <div id="environment"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 Detailed Logs</h3>
            <div id="logs" class="log-area"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Raw Response Data</h3>
            <div id="raw-data" class="details"></div>
        </div>
    </div>

    <script>
        let logs = [];
        let testResults = {};

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logsDiv = document.getElementById('logs');
            logsDiv.textContent = logs.join('\n');
            logsDiv.scrollTop = logsDiv.scrollHeight;
            
            console.log(logEntry);
        }

        function updateResults() {
            const resultsDiv = document.getElementById('results');
            let html = '';
            
            for (const [test, result] of Object.entries(testResults)) {
                const statusClass = result.success ? 'success' : 'error';
                html += `<div class="status ${statusClass}">
                    ${result.success ? '✅' : '❌'} ${test}: ${result.message}
                </div>`;
            }
            
            resultsDiv.innerHTML = html;
        }

        function updateEnvironment() {
            const envDiv = document.getElementById('environment');
            const info = {
                'User Agent': navigator.userAgent,
                'URL': window.location.href,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'Has Microphone API': !!navigator.mediaDevices,
                'Has getUserMedia': !!navigator.mediaDevices?.getUserMedia,
                'Is HTTPS': window.location.protocol === 'https:',
                'Local Storage': !!window.localStorage,
                'Session Storage': !!window.sessionStorage
            };
            
            let html = '';
            for (const [key, value] of Object.entries(info)) {
                const statusClass = value === true ? 'success' : value === false ? 'error' : 'info';
                html += `<div class="status ${statusClass}">${key}: ${value}</div>`;
            }
            
            envDiv.innerHTML = html;
        }

        function updateRawData(data) {
            const rawDiv = document.getElementById('raw-data');
            rawDiv.textContent = JSON.stringify(data, null, 2);
        }

        function clearLogs() {
            logs = [];
            testResults = {};
            document.getElementById('logs').textContent = '';
            document.getElementById('results').innerHTML = '';
            document.getElementById('raw-data').textContent = '';
        }

        async function testApiKey() {
            log('🔑 Testing API Key Authentication...');

            try {
                // Use the known working API key directly
                const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
                log(`Using API key: ${apiKey.substring(0, 8)}...`);

                // Test multiple endpoints to understand the issue
                const endpoints = [
                    { name: 'Assistants List', url: 'https://api.vapi.ai/assistant' },
                    { name: 'Account Info', url: 'https://api.vapi.ai/account' },
                    { name: 'Calls List', url: 'https://api.vapi.ai/call' }
                ];

                for (const endpoint of endpoints) {
                    try {
                        log(`Testing ${endpoint.name}...`);

                        const apiResponse = await fetch(endpoint.url, {
                            headers: {
                                'Authorization': `Bearer ${apiKey}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        const responseData = {
                            endpoint: endpoint.name,
                            status: apiResponse.status,
                            statusText: apiResponse.statusText,
                            headers: Object.fromEntries(apiResponse.headers.entries())
                        };

                        if (apiResponse.ok) {
                            const data = await apiResponse.json();
                            responseData.data = data;
                            testResults[`${endpoint.name} API`] = {
                                success: true,
                                message: `Success - ${data.length ? data.length + ' items' : 'Data received'}`
                            };
                            log(`✅ ${endpoint.name}: Success`);
                        } else {
                            const errorData = await apiResponse.text();
                            responseData.error = errorData;
                            testResults[`${endpoint.name} API`] = {
                                success: false,
                                message: `HTTP ${apiResponse.status}: ${errorData}`
                            };
                            log(`❌ ${endpoint.name}: ${apiResponse.status} - ${errorData}`);
                        }

                        updateRawData(responseData);

                    } catch (endpointError) {
                        testResults[`${endpoint.name} API`] = {
                            success: false,
                            message: `Error: ${endpointError.message}`
                        };
                        log(`❌ ${endpoint.name} failed: ${endpointError.message}`);
                    }
                }

            } catch (error) {
                testResults['API Key Auth'] = {
                    success: false,
                    message: `Error: ${error.message}`
                };
                log(`❌ API Key test failed: ${error.message}`);
            }

            updateResults();
        }

        async function testWebSDK() {
            log('🌐 Testing Vapi Web SDK...');
            
            try {
                // Test if we can load the Vapi SDK
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/vapi.js';
                
                const loadPromise = new Promise((resolve, reject) => {
                    script.onload = resolve;
                    script.onerror = reject;
                    setTimeout(() => reject(new Error('SDK load timeout')), 10000);
                });
                
                document.head.appendChild(script);
                await loadPromise;
                
                log('✅ Vapi SDK loaded successfully');
                
                // Test SDK initialization
                if (typeof window.Vapi !== 'undefined') {
                    testResults['SDK Load'] = {
                        success: true,
                        message: 'SDK loaded and available'
                    };
                    
                    // Try to create instance
                    try {
                        const testKey = '6734febc-fc65-4669-93b0-929b31ff6564';
                        const vapi = new window.Vapi(testKey);
                        
                        testResults['SDK Instance'] = {
                            success: true,
                            message: 'Instance created successfully'
                        };
                        log('✅ Vapi instance created');
                        
                        // Test if instance has required methods
                        const requiredMethods = ['start', 'stop', 'on', 'off'];
                        const missingMethods = requiredMethods.filter(method => typeof vapi[method] !== 'function');
                        
                        if (missingMethods.length === 0) {
                            testResults['SDK Methods'] = {
                                success: true,
                                message: 'All required methods available'
                            };
                            log('✅ All required methods available');
                        } else {
                            testResults['SDK Methods'] = {
                                success: false,
                                message: `Missing methods: ${missingMethods.join(', ')}`
                            };
                            log(`❌ Missing methods: ${missingMethods.join(', ')}`);
                        }
                        
                    } catch (error) {
                        testResults['SDK Instance'] = {
                            success: false,
                            message: `Instance creation failed: ${error.message}`
                        };
                        log(`❌ Instance creation failed: ${error.message}`);
                    }
                    
                } else {
                    testResults['SDK Load'] = {
                        success: false,
                        message: 'SDK loaded but Vapi not available'
                    };
                    log('❌ SDK loaded but Vapi constructor not found');
                }
                
            } catch (error) {
                testResults['SDK Load'] = {
                    success: false,
                    message: `SDK load failed: ${error.message}`
                };
                log(`❌ SDK load failed: ${error.message}`);
            }
            
            updateResults();
        }

        async function testMicrophoneAccess() {
            log('🎤 Testing Microphone Access...');
            
            try {
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    testResults['Microphone API'] = {
                        success: false,
                        message: 'getUserMedia not supported'
                    };
                    log('❌ getUserMedia not supported');
                    updateResults();
                    return;
                }
                
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                testResults['Microphone Access'] = {
                    success: true,
                    message: 'Microphone access granted'
                };
                log('✅ Microphone access granted');
                
                // Clean up
                stream.getTracks().forEach(track => track.stop());
                
            } catch (error) {
                testResults['Microphone Access'] = {
                    success: false,
                    message: `Microphone denied: ${error.message}`
                };
                log(`❌ Microphone access denied: ${error.message}`);
            }
            
            updateResults();
        }

        async function testCallOrchestration() {
            log('📞 Testing Call Orchestration...');

            try {
                // Test if we can create a Vapi instance and attempt a call
                if (typeof window.Vapi === 'undefined') {
                    testResults['Call Orchestration'] = {
                        success: false,
                        message: 'Vapi SDK not loaded'
                    };
                    updateResults();
                    return;
                }

                const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
                const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a'; // Uses OpenAI Echo voice

                log(`Creating Vapi instance with key: ${apiKey.substring(0, 8)}...`);
                const vapi = new window.Vapi(apiKey);

                // Set up event listeners to capture what happens
                const events = [];

                vapi.on('call-start', () => {
                    events.push('call-start');
                    log('📞 Event: call-start');
                });

                vapi.on('call-end', () => {
                    events.push('call-end');
                    log('📞 Event: call-end');
                });

                vapi.on('error', (error) => {
                    events.push(`error: ${error.message || error}`);
                    log(`❌ Event: error - ${error.message || error}`);
                });

                vapi.on('speech-start', () => {
                    events.push('speech-start');
                    log('🗣️ Event: speech-start');
                });

                vapi.on('speech-end', () => {
                    events.push('speech-end');
                    log('🗣️ Event: speech-end');
                });

                // Try to start a call
                log(`Attempting to start call with assistant: ${assistantId}`);

                try {
                    const callResult = await vapi.start(assistantId);

                    testResults['Call Start'] = {
                        success: true,
                        message: `Call started successfully`
                    };
                    log('✅ Call started successfully');

                    // Wait a moment to see if we get any events
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    // Stop the call
                    vapi.stop();

                    testResults['Call Events'] = {
                        success: events.length > 0,
                        message: `Events received: ${events.join(', ') || 'None'}`
                    };

                } catch (callError) {
                    testResults['Call Start'] = {
                        success: false,
                        message: `Call failed: ${callError.message}`
                    };
                    log(`❌ Call failed: ${callError.message}`);

                    // Capture the full error details
                    updateRawData({
                        error: callError.message,
                        stack: callError.stack,
                        events: events
                    });
                }

            } catch (error) {
                testResults['Call Orchestration'] = {
                    success: false,
                    message: `Orchestration failed: ${error.message}`
                };
                log(`❌ Call orchestration failed: ${error.message}`);
            }

            updateResults();
        }

        async function runFullDiagnosis() {
            log('🚀 Starting Full Vapi Orchestration Diagnosis...');
            clearLogs();

            updateEnvironment();

            await testApiKey();
            await testWebSDK();
            await testMicrophoneAccess();
            await testCallOrchestration();

            log('🏁 Diagnosis complete!');

            // Provide summary and recommendations
            const failedTests = Object.entries(testResults).filter(([_, result]) => !result.success);
            if (failedTests.length > 0) {
                log('');
                log('🔧 ISSUES FOUND:');
                failedTests.forEach(([test, result]) => {
                    log(`   ❌ ${test}: ${result.message}`);
                });

                log('');
                log('💡 RECOMMENDATIONS:');
                if (failedTests.some(([test]) => test.includes('API'))) {
                    log('   • Check API key validity and permissions');
                }
                if (failedTests.some(([test]) => test.includes('SDK'))) {
                    log('   • Verify Vapi Web SDK loading and compatibility');
                }
                if (failedTests.some(([test]) => test.includes('Microphone'))) {
                    log('   • Ensure HTTPS and microphone permissions');
                }
                if (failedTests.some(([test]) => test.includes('Call'))) {
                    log('   • Check assistant ID and call parameters');
                    log('   • Verify network connectivity to Vapi servers');
                }
            } else {
                log('');
                log('🎉 All tests passed! The issue may be in your application code.');
            }
        }

        // Initialize
        updateEnvironment();
        log('🔍 Vapi Orchestration Diagnosis Tool Ready');
    </script>
</body>
</html>
