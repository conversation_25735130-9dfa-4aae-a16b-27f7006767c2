/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface User {
    /** This is the unique identifier for the profile or user. */
    id: string;
    /** This is the ISO 8601 date-time string of when the profile was created. */
    createdAt: string;
    /** This is the ISO 8601 date-time string of when the profile was last updated. */
    updatedAt: string;
    /** This is the email of the user that is associated with the profile. */
    email: string;
    /** This is the full name of the user that is associated with the profile. */
    fullName?: string;
}
