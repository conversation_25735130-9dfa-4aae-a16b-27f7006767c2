/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface VapiVoice {
    /** This is the voice provider that will be used. */
    provider: "vapi";
    /** The voices provided by Vapi */
    voiceId: Vapi.VapiVoiceVoiceId;
    /**
     * This is the speed multiplier that will be used.
     *
     * @default 1
     */
    speed?: number;
    /**
     * This is the language code (ISO 639-1) that will be used.
     *
     * @default 'en-US'
     */
    language?: Vapi.VapiVoiceLanguage;
    /** This is the plan for chunking the model output before it is sent to the voice provider. */
    chunkPlan?: Vapi.ChunkPlan;
    /** This is the plan for voice provider fallbacks in the event that the primary voice provider fails. */
    fallbackPlan?: Vapi.FallbackPlan;
}
