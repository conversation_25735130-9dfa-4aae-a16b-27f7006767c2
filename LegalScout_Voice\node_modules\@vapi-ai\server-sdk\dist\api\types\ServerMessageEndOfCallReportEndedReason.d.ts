/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * This is the reason the call ended. This can also be found at `call.endedReason` on GET /call/:id.
 */
export type ServerMessageEndOfCallReportEndedReason = "call-start-error-neither-assistant-nor-server-set" | "assistant-request-failed" | "assistant-request-returned-error" | "assistant-request-returned-unspeakable-error" | "assistant-request-returned-invalid-assistant" | "assistant-request-returned-no-assistant" | "assistant-request-returned-forwarding-phone-number" | "call.start.error-get-org" | "call.start.error-get-subscription" | "call.start.error-get-assistant" | "call.start.error-get-phone-number" | "call.start.error-get-customer" | "call.start.error-get-resources-validation" | "call.start.error-vapi-number-international" | "call.start.error-vapi-number-outbound-daily-limit" | "call.start.error-get-transport" | "assistant-not-valid" | "database-error" | "assistant-not-found" | "pipeline-error-openai-voice-failed" | "pipeline-error-cartesia-voice-failed" | "pipeline-error-deepgram-voice-failed" | "pipeline-error-eleven-labs-voice-failed" | "pipeline-error-playht-voice-failed" | "pipeline-error-lmnt-voice-failed" | "pipeline-error-azure-voice-failed" | "pipeline-error-rime-ai-voice-failed" | "pipeline-error-smallest-ai-voice-failed" | "pipeline-error-neuphonic-voice-failed" | "pipeline-error-hume-voice-failed" | "pipeline-error-sesame-voice-failed" | "pipeline-error-tavus-video-failed" | "call.in-progress.error-vapifault-openai-voice-failed" | "call.in-progress.error-vapifault-cartesia-voice-failed" | "call.in-progress.error-vapifault-deepgram-voice-failed" | "call.in-progress.error-vapifault-eleven-labs-voice-failed" | "call.in-progress.error-vapifault-playht-voice-failed" | "call.in-progress.error-vapifault-lmnt-voice-failed" | "call.in-progress.error-vapifault-azure-voice-failed" | "call.in-progress.error-vapifault-rime-ai-voice-failed" | "call.in-progress.error-vapifault-smallest-ai-voice-failed" | "call.in-progress.error-vapifault-neuphonic-voice-failed" | "call.in-progress.error-vapifault-hume-voice-failed" | "call.in-progress.error-vapifault-sesame-voice-failed" | "call.in-progress.error-vapifault-tavus-video-failed" | "pipeline-error-vapi-llm-failed" | "pipeline-error-vapi-400-bad-request-validation-failed" | "pipeline-error-vapi-401-unauthorized" | "pipeline-error-vapi-403-model-access-denied" | "pipeline-error-vapi-429-exceeded-quota" | "pipeline-error-vapi-500-server-error" | "pipeline-error-vapi-503-server-overloaded-error" | "call.in-progress.error-vapifault-vapi-llm-failed" | "call.in-progress.error-vapifault-vapi-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-vapi-401-unauthorized" | "call.in-progress.error-vapifault-vapi-403-model-access-denied" | "call.in-progress.error-vapifault-vapi-429-exceeded-quota" | "call.in-progress.error-providerfault-vapi-500-server-error" | "call.in-progress.error-providerfault-vapi-503-server-overloaded-error" | "pipeline-error-deepgram-transcriber-failed" | "call.in-progress.error-vapifault-deepgram-transcriber-failed" | "pipeline-error-gladia-transcriber-failed" | "call.in-progress.error-vapifault-gladia-transcriber-failed" | "pipeline-error-speechmatics-transcriber-failed" | "call.in-progress.error-vapifault-speechmatics-transcriber-failed" | "pipeline-error-assembly-ai-transcriber-failed" | "pipeline-error-assembly-ai-returning-400-insufficent-funds" | "pipeline-error-assembly-ai-returning-400-paid-only-feature" | "pipeline-error-assembly-ai-returning-401-invalid-credentials" | "pipeline-error-assembly-ai-returning-500-invalid-schema" | "pipeline-error-assembly-ai-returning-500-word-boost-parsing-failed" | "call.in-progress.error-vapifault-assembly-ai-transcriber-failed" | "call.in-progress.error-vapifault-assembly-ai-returning-400-insufficent-funds" | "call.in-progress.error-vapifault-assembly-ai-returning-400-paid-only-feature" | "call.in-progress.error-vapifault-assembly-ai-returning-401-invalid-credentials" | "call.in-progress.error-vapifault-assembly-ai-returning-500-invalid-schema" | "call.in-progress.error-vapifault-assembly-ai-returning-500-word-boost-parsing-failed" | "pipeline-error-talkscriber-transcriber-failed" | "call.in-progress.error-vapifault-talkscriber-transcriber-failed" | "pipeline-error-azure-speech-transcriber-failed" | "call.in-progress.error-vapifault-azure-speech-transcriber-failed" | "call.in-progress.error-pipeline-no-available-llm-model" | "worker-shutdown" | "unknown-error" | "vonage-disconnected" | "vonage-failed-to-connect-call" | "vonage-completed" | "phone-call-provider-bypass-enabled-but-no-call-received" | "call.in-progress.error-vapifault-transport-never-connected" | "call.in-progress.error-vapifault-transport-connected-but-call-not-active" | "call.in-progress.error-vapifault-call-started-but-connection-to-transport-missing" | "call.in-progress.error-vapifault-openai-llm-failed" | "call.in-progress.error-vapifault-azure-openai-llm-failed" | "call.in-progress.error-vapifault-groq-llm-failed" | "call.in-progress.error-vapifault-google-llm-failed" | "call.in-progress.error-vapifault-xai-llm-failed" | "call.in-progress.error-vapifault-mistral-llm-failed" | "call.in-progress.error-vapifault-inflection-ai-llm-failed" | "call.in-progress.error-vapifault-cerebras-llm-failed" | "call.in-progress.error-vapifault-deep-seek-llm-failed" | "pipeline-error-openai-400-bad-request-validation-failed" | "pipeline-error-openai-401-unauthorized" | "pipeline-error-openai-401-incorrect-api-key" | "pipeline-error-openai-401-account-not-in-organization" | "pipeline-error-openai-403-model-access-denied" | "pipeline-error-openai-429-exceeded-quota" | "pipeline-error-openai-429-rate-limit-reached" | "pipeline-error-openai-500-server-error" | "pipeline-error-openai-503-server-overloaded-error" | "pipeline-error-openai-llm-failed" | "call.in-progress.error-vapifault-openai-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-openai-401-unauthorized" | "call.in-progress.error-vapifault-openai-401-incorrect-api-key" | "call.in-progress.error-vapifault-openai-401-account-not-in-organization" | "call.in-progress.error-vapifault-openai-403-model-access-denied" | "call.in-progress.error-vapifault-openai-429-exceeded-quota" | "call.in-progress.error-vapifault-openai-429-rate-limit-reached" | "call.in-progress.error-providerfault-openai-500-server-error" | "call.in-progress.error-providerfault-openai-503-server-overloaded-error" | "pipeline-error-azure-openai-400-bad-request-validation-failed" | "pipeline-error-azure-openai-401-unauthorized" | "pipeline-error-azure-openai-403-model-access-denied" | "pipeline-error-azure-openai-429-exceeded-quota" | "pipeline-error-azure-openai-500-server-error" | "pipeline-error-azure-openai-503-server-overloaded-error" | "pipeline-error-azure-openai-llm-failed" | "call.in-progress.error-vapifault-azure-openai-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-azure-openai-401-unauthorized" | "call.in-progress.error-vapifault-azure-openai-403-model-access-denied" | "call.in-progress.error-vapifault-azure-openai-429-exceeded-quota" | "call.in-progress.error-providerfault-azure-openai-500-server-error" | "call.in-progress.error-providerfault-azure-openai-503-server-overloaded-error" | "pipeline-error-google-400-bad-request-validation-failed" | "pipeline-error-google-401-unauthorized" | "pipeline-error-google-403-model-access-denied" | "pipeline-error-google-429-exceeded-quota" | "pipeline-error-google-500-server-error" | "pipeline-error-google-503-server-overloaded-error" | "pipeline-error-google-llm-failed" | "call.in-progress.error-vapifault-google-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-google-401-unauthorized" | "call.in-progress.error-vapifault-google-403-model-access-denied" | "call.in-progress.error-vapifault-google-429-exceeded-quota" | "call.in-progress.error-providerfault-google-500-server-error" | "call.in-progress.error-providerfault-google-503-server-overloaded-error" | "pipeline-error-xai-400-bad-request-validation-failed" | "pipeline-error-xai-401-unauthorized" | "pipeline-error-xai-403-model-access-denied" | "pipeline-error-xai-429-exceeded-quota" | "pipeline-error-xai-500-server-error" | "pipeline-error-xai-503-server-overloaded-error" | "pipeline-error-xai-llm-failed" | "call.in-progress.error-vapifault-xai-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-xai-401-unauthorized" | "call.in-progress.error-vapifault-xai-403-model-access-denied" | "call.in-progress.error-vapifault-xai-429-exceeded-quota" | "call.in-progress.error-providerfault-xai-500-server-error" | "call.in-progress.error-providerfault-xai-503-server-overloaded-error" | "pipeline-error-mistral-400-bad-request-validation-failed" | "pipeline-error-mistral-401-unauthorized" | "pipeline-error-mistral-403-model-access-denied" | "pipeline-error-mistral-429-exceeded-quota" | "pipeline-error-mistral-500-server-error" | "pipeline-error-mistral-503-server-overloaded-error" | "pipeline-error-mistral-llm-failed" | "call.in-progress.error-vapifault-mistral-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-mistral-401-unauthorized" | "call.in-progress.error-vapifault-mistral-403-model-access-denied" | "call.in-progress.error-vapifault-mistral-429-exceeded-quota" | "call.in-progress.error-providerfault-mistral-500-server-error" | "call.in-progress.error-providerfault-mistral-503-server-overloaded-error" | "pipeline-error-inflection-ai-400-bad-request-validation-failed" | "pipeline-error-inflection-ai-401-unauthorized" | "pipeline-error-inflection-ai-403-model-access-denied" | "pipeline-error-inflection-ai-429-exceeded-quota" | "pipeline-error-inflection-ai-500-server-error" | "pipeline-error-inflection-ai-503-server-overloaded-error" | "pipeline-error-inflection-ai-llm-failed" | "call.in-progress.error-vapifault-inflection-ai-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-inflection-ai-401-unauthorized" | "call.in-progress.error-vapifault-inflection-ai-403-model-access-denied" | "call.in-progress.error-vapifault-inflection-ai-429-exceeded-quota" | "call.in-progress.error-providerfault-inflection-ai-500-server-error" | "call.in-progress.error-providerfault-inflection-ai-503-server-overloaded-error" | "pipeline-error-deep-seek-400-bad-request-validation-failed" | "pipeline-error-deep-seek-401-unauthorized" | "pipeline-error-deep-seek-403-model-access-denied" | "pipeline-error-deep-seek-429-exceeded-quota" | "pipeline-error-deep-seek-500-server-error" | "pipeline-error-deep-seek-503-server-overloaded-error" | "pipeline-error-deep-seek-llm-failed" | "call.in-progress.error-vapifault-deep-seek-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-deep-seek-401-unauthorized" | "call.in-progress.error-vapifault-deep-seek-403-model-access-denied" | "call.in-progress.error-vapifault-deep-seek-429-exceeded-quota" | "call.in-progress.error-providerfault-deep-seek-500-server-error" | "call.in-progress.error-providerfault-deep-seek-503-server-overloaded-error" | "pipeline-error-groq-400-bad-request-validation-failed" | "pipeline-error-groq-401-unauthorized" | "pipeline-error-groq-403-model-access-denied" | "pipeline-error-groq-429-exceeded-quota" | "pipeline-error-groq-500-server-error" | "pipeline-error-groq-503-server-overloaded-error" | "pipeline-error-groq-llm-failed" | "call.in-progress.error-vapifault-groq-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-groq-401-unauthorized" | "call.in-progress.error-vapifault-groq-403-model-access-denied" | "call.in-progress.error-vapifault-groq-429-exceeded-quota" | "call.in-progress.error-providerfault-groq-500-server-error" | "call.in-progress.error-providerfault-groq-503-server-overloaded-error" | "pipeline-error-cerebras-400-bad-request-validation-failed" | "pipeline-error-cerebras-401-unauthorized" | "pipeline-error-cerebras-403-model-access-denied" | "pipeline-error-cerebras-429-exceeded-quota" | "pipeline-error-cerebras-500-server-error" | "pipeline-error-cerebras-503-server-overloaded-error" | "pipeline-error-cerebras-llm-failed" | "call.in-progress.error-vapifault-cerebras-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-cerebras-401-unauthorized" | "call.in-progress.error-vapifault-cerebras-403-model-access-denied" | "call.in-progress.error-vapifault-cerebras-429-exceeded-quota" | "call.in-progress.error-providerfault-cerebras-500-server-error" | "call.in-progress.error-providerfault-cerebras-503-server-overloaded-error" | "pipeline-error-anthropic-400-bad-request-validation-failed" | "pipeline-error-anthropic-401-unauthorized" | "pipeline-error-anthropic-403-model-access-denied" | "pipeline-error-anthropic-429-exceeded-quota" | "pipeline-error-anthropic-500-server-error" | "pipeline-error-anthropic-503-server-overloaded-error" | "pipeline-error-anthropic-llm-failed" | "call.in-progress.error-vapifault-anthropic-llm-failed" | "call.in-progress.error-vapifault-anthropic-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-anthropic-401-unauthorized" | "call.in-progress.error-vapifault-anthropic-403-model-access-denied" | "call.in-progress.error-vapifault-anthropic-429-exceeded-quota" | "call.in-progress.error-providerfault-anthropic-500-server-error" | "call.in-progress.error-providerfault-anthropic-503-server-overloaded-error" | "pipeline-error-together-ai-400-bad-request-validation-failed" | "pipeline-error-together-ai-401-unauthorized" | "pipeline-error-together-ai-403-model-access-denied" | "pipeline-error-together-ai-429-exceeded-quota" | "pipeline-error-together-ai-500-server-error" | "pipeline-error-together-ai-503-server-overloaded-error" | "pipeline-error-together-ai-llm-failed" | "call.in-progress.error-vapifault-together-ai-llm-failed" | "call.in-progress.error-vapifault-together-ai-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-together-ai-401-unauthorized" | "call.in-progress.error-vapifault-together-ai-403-model-access-denied" | "call.in-progress.error-vapifault-together-ai-429-exceeded-quota" | "call.in-progress.error-providerfault-together-ai-500-server-error" | "call.in-progress.error-providerfault-together-ai-503-server-overloaded-error" | "pipeline-error-anyscale-400-bad-request-validation-failed" | "pipeline-error-anyscale-401-unauthorized" | "pipeline-error-anyscale-403-model-access-denied" | "pipeline-error-anyscale-429-exceeded-quota" | "pipeline-error-anyscale-500-server-error" | "pipeline-error-anyscale-503-server-overloaded-error" | "pipeline-error-anyscale-llm-failed" | "call.in-progress.error-vapifault-anyscale-llm-failed" | "call.in-progress.error-vapifault-anyscale-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-anyscale-401-unauthorized" | "call.in-progress.error-vapifault-anyscale-403-model-access-denied" | "call.in-progress.error-vapifault-anyscale-429-exceeded-quota" | "call.in-progress.error-providerfault-anyscale-500-server-error" | "call.in-progress.error-providerfault-anyscale-503-server-overloaded-error" | "pipeline-error-openrouter-400-bad-request-validation-failed" | "pipeline-error-openrouter-401-unauthorized" | "pipeline-error-openrouter-403-model-access-denied" | "pipeline-error-openrouter-429-exceeded-quota" | "pipeline-error-openrouter-500-server-error" | "pipeline-error-openrouter-503-server-overloaded-error" | "pipeline-error-openrouter-llm-failed" | "call.in-progress.error-vapifault-openrouter-llm-failed" | "call.in-progress.error-vapifault-openrouter-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-openrouter-401-unauthorized" | "call.in-progress.error-vapifault-openrouter-403-model-access-denied" | "call.in-progress.error-vapifault-openrouter-429-exceeded-quota" | "call.in-progress.error-providerfault-openrouter-500-server-error" | "call.in-progress.error-providerfault-openrouter-503-server-overloaded-error" | "pipeline-error-perplexity-ai-400-bad-request-validation-failed" | "pipeline-error-perplexity-ai-401-unauthorized" | "pipeline-error-perplexity-ai-403-model-access-denied" | "pipeline-error-perplexity-ai-429-exceeded-quota" | "pipeline-error-perplexity-ai-500-server-error" | "pipeline-error-perplexity-ai-503-server-overloaded-error" | "pipeline-error-perplexity-ai-llm-failed" | "call.in-progress.error-vapifault-perplexity-ai-llm-failed" | "call.in-progress.error-vapifault-perplexity-ai-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-perplexity-ai-401-unauthorized" | "call.in-progress.error-vapifault-perplexity-ai-403-model-access-denied" | "call.in-progress.error-vapifault-perplexity-ai-429-exceeded-quota" | "call.in-progress.error-providerfault-perplexity-ai-500-server-error" | "call.in-progress.error-providerfault-perplexity-ai-503-server-overloaded-error" | "pipeline-error-deepinfra-400-bad-request-validation-failed" | "pipeline-error-deepinfra-401-unauthorized" | "pipeline-error-deepinfra-403-model-access-denied" | "pipeline-error-deepinfra-429-exceeded-quota" | "pipeline-error-deepinfra-500-server-error" | "pipeline-error-deepinfra-503-server-overloaded-error" | "pipeline-error-deepinfra-llm-failed" | "call.in-progress.error-vapifault-deepinfra-llm-failed" | "call.in-progress.error-vapifault-deepinfra-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-deepinfra-401-unauthorized" | "call.in-progress.error-vapifault-deepinfra-403-model-access-denied" | "call.in-progress.error-vapifault-deepinfra-429-exceeded-quota" | "call.in-progress.error-providerfault-deepinfra-500-server-error" | "call.in-progress.error-providerfault-deepinfra-503-server-overloaded-error" | "pipeline-error-runpod-400-bad-request-validation-failed" | "pipeline-error-runpod-401-unauthorized" | "pipeline-error-runpod-403-model-access-denied" | "pipeline-error-runpod-429-exceeded-quota" | "pipeline-error-runpod-500-server-error" | "pipeline-error-runpod-503-server-overloaded-error" | "pipeline-error-runpod-llm-failed" | "call.in-progress.error-vapifault-runpod-llm-failed" | "call.in-progress.error-vapifault-runpod-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-runpod-401-unauthorized" | "call.in-progress.error-vapifault-runpod-403-model-access-denied" | "call.in-progress.error-vapifault-runpod-429-exceeded-quota" | "call.in-progress.error-providerfault-runpod-500-server-error" | "call.in-progress.error-providerfault-runpod-503-server-overloaded-error" | "pipeline-error-custom-llm-400-bad-request-validation-failed" | "pipeline-error-custom-llm-401-unauthorized" | "pipeline-error-custom-llm-403-model-access-denied" | "pipeline-error-custom-llm-429-exceeded-quota" | "pipeline-error-custom-llm-500-server-error" | "pipeline-error-custom-llm-503-server-overloaded-error" | "pipeline-error-custom-llm-llm-failed" | "call.in-progress.error-vapifault-custom-llm-llm-failed" | "call.in-progress.error-vapifault-custom-llm-400-bad-request-validation-failed" | "call.in-progress.error-vapifault-custom-llm-401-unauthorized" | "call.in-progress.error-vapifault-custom-llm-403-model-access-denied" | "call.in-progress.error-vapifault-custom-llm-429-exceeded-quota" | "call.in-progress.error-providerfault-custom-llm-500-server-error" | "call.in-progress.error-providerfault-custom-llm-503-server-overloaded-error" | "pipeline-error-custom-voice-failed" | "pipeline-error-cartesia-socket-hang-up" | "pipeline-error-cartesia-requested-payment" | "pipeline-error-cartesia-500-server-error" | "pipeline-error-cartesia-503-server-error" | "pipeline-error-cartesia-522-server-error" | "call.in-progress.error-vapifault-cartesia-socket-hang-up" | "call.in-progress.error-vapifault-cartesia-requested-payment" | "call.in-progress.error-providerfault-cartesia-500-server-error" | "call.in-progress.error-providerfault-cartesia-503-server-error" | "call.in-progress.error-providerfault-cartesia-522-server-error" | "pipeline-error-eleven-labs-voice-not-found" | "pipeline-error-eleven-labs-quota-exceeded" | "pipeline-error-eleven-labs-unauthorized-access" | "pipeline-error-eleven-labs-unauthorized-to-access-model" | "pipeline-error-eleven-labs-professional-voices-only-for-creator-plus" | "pipeline-error-eleven-labs-blocked-free-plan-and-requested-upgrade" | "pipeline-error-eleven-labs-blocked-concurrent-requests-and-requested-upgrade" | "pipeline-error-eleven-labs-blocked-using-instant-voice-clone-and-requested-upgrade" | "pipeline-error-eleven-labs-system-busy-and-requested-upgrade" | "pipeline-error-eleven-labs-voice-not-fine-tuned" | "pipeline-error-eleven-labs-invalid-api-key" | "pipeline-error-eleven-labs-invalid-voice-samples" | "pipeline-error-eleven-labs-voice-disabled-by-owner" | "pipeline-error-eleven-labs-blocked-account-in-probation" | "pipeline-error-eleven-labs-blocked-content-against-their-policy" | "pipeline-error-eleven-labs-missing-samples-for-voice-clone" | "pipeline-error-eleven-labs-voice-not-fine-tuned-and-cannot-be-used" | "pipeline-error-eleven-labs-voice-not-allowed-for-free-users" | "pipeline-error-eleven-labs-max-character-limit-exceeded" | "pipeline-error-eleven-labs-blocked-voice-potentially-against-terms-of-service-and-awaiting-verification" | "pipeline-error-eleven-labs-500-server-error" | "call.in-progress.error-vapifault-eleven-labs-voice-not-found" | "call.in-progress.error-vapifault-eleven-labs-quota-exceeded" | "call.in-progress.error-vapifault-eleven-labs-unauthorized-access" | "call.in-progress.error-vapifault-eleven-labs-unauthorized-to-access-model" | "call.in-progress.error-vapifault-eleven-labs-professional-voices-only-for-creator-plus" | "call.in-progress.error-vapifault-eleven-labs-blocked-free-plan-and-requested-upgrade" | "call.in-progress.error-vapifault-eleven-labs-blocked-concurrent-requests-and-requested-upgrade" | "call.in-progress.error-vapifault-eleven-labs-blocked-using-instant-voice-clone-and-requested-upgrade" | "call.in-progress.error-vapifault-eleven-labs-system-busy-and-requested-upgrade" | "call.in-progress.error-vapifault-eleven-labs-voice-not-fine-tuned" | "call.in-progress.error-vapifault-eleven-labs-invalid-api-key" | "call.in-progress.error-vapifault-eleven-labs-invalid-voice-samples" | "call.in-progress.error-vapifault-eleven-labs-voice-disabled-by-owner" | "call.in-progress.error-vapifault-eleven-labs-blocked-account-in-probation" | "call.in-progress.error-vapifault-eleven-labs-blocked-content-against-their-policy" | "call.in-progress.error-vapifault-eleven-labs-missing-samples-for-voice-clone" | "call.in-progress.error-vapifault-eleven-labs-voice-not-fine-tuned-and-cannot-be-used" | "call.in-progress.error-vapifault-eleven-labs-voice-not-allowed-for-free-users" | "call.in-progress.error-vapifault-eleven-labs-max-character-limit-exceeded" | "call.in-progress.error-vapifault-eleven-labs-blocked-voice-potentially-against-terms-of-service-and-awaiting-verification" | "call.in-progress.error-providerfault-eleven-labs-500-server-error" | "pipeline-error-playht-request-timed-out" | "pipeline-error-playht-invalid-voice" | "pipeline-error-playht-unexpected-error" | "pipeline-error-playht-out-of-credits" | "pipeline-error-playht-invalid-emotion" | "pipeline-error-playht-voice-must-be-a-valid-voice-manifest-uri" | "pipeline-error-playht-401-unauthorized" | "pipeline-error-playht-403-forbidden-out-of-characters" | "pipeline-error-playht-403-forbidden-api-access-not-available" | "pipeline-error-playht-429-exceeded-quota" | "pipeline-error-playht-502-gateway-error" | "pipeline-error-playht-504-gateway-error" | "call.in-progress.error-vapifault-playht-request-timed-out" | "call.in-progress.error-vapifault-playht-invalid-voice" | "call.in-progress.error-vapifault-playht-unexpected-error" | "call.in-progress.error-vapifault-playht-out-of-credits" | "call.in-progress.error-vapifault-playht-invalid-emotion" | "call.in-progress.error-vapifault-playht-voice-must-be-a-valid-voice-manifest-uri" | "call.in-progress.error-vapifault-playht-401-unauthorized" | "call.in-progress.error-vapifault-playht-403-forbidden-out-of-characters" | "call.in-progress.error-vapifault-playht-403-forbidden-api-access-not-available" | "call.in-progress.error-vapifault-playht-429-exceeded-quota" | "call.in-progress.error-providerfault-playht-502-gateway-error" | "call.in-progress.error-providerfault-playht-504-gateway-error" | "pipeline-error-custom-transcriber-failed" | "call.in-progress.error-vapifault-custom-transcriber-failed" | "pipeline-error-11labs-transcriber-failed" | "call.in-progress.error-vapifault-11labs-transcriber-failed" | "pipeline-error-deepgram-returning-400-no-such-model-language-tier-combination" | "pipeline-error-deepgram-returning-401-invalid-credentials" | "pipeline-error-deepgram-returning-403-model-access-denied" | "pipeline-error-deepgram-returning-404-not-found" | "pipeline-error-deepgram-returning-500-invalid-json" | "pipeline-error-deepgram-returning-502-network-error" | "pipeline-error-deepgram-returning-502-bad-gateway-ehostunreach" | "call.in-progress.error-vapifault-deepgram-returning-400-no-such-model-language-tier-combination" | "call.in-progress.error-vapifault-deepgram-returning-401-invalid-credentials" | "call.in-progress.error-vapifault-deepgram-returning-404-not-found" | "call.in-progress.error-vapifault-deepgram-returning-403-model-access-denied" | "call.in-progress.error-providerfault-deepgram-returning-500-invalid-json" | "call.in-progress.error-providerfault-deepgram-returning-502-network-error" | "call.in-progress.error-providerfault-deepgram-returning-502-bad-gateway-ehostunreach" | "pipeline-error-google-transcriber-failed" | "call.in-progress.error-vapifault-google-transcriber-failed" | "pipeline-error-openai-transcriber-failed" | "call.in-progress.error-vapifault-openai-transcriber-failed" | "assistant-ended-call" | "assistant-said-end-call-phrase" | "assistant-ended-call-with-hangup-task" | "assistant-ended-call-after-message-spoken" | "assistant-forwarded-call" | "assistant-join-timed-out" | "call.in-progress.error-assistant-did-not-receive-customer-audio" | "customer-busy" | "customer-ended-call" | "customer-did-not-answer" | "customer-did-not-give-microphone-permission" | "exceeded-max-duration" | "manually-canceled" | "phone-call-provider-closed-websocket" | "silence-timed-out" | "call.in-progress.error-sip-telephony-provider-failed-to-connect-call" | "twilio-failed-to-connect-call" | "twilio-reported-customer-misdialed" | "vonage-rejected" | "voicemail";
export declare const ServerMessageEndOfCallReportEndedReason: {
    readonly CallStartErrorNeitherAssistantNorServerSet: "call-start-error-neither-assistant-nor-server-set";
    readonly AssistantRequestFailed: "assistant-request-failed";
    readonly AssistantRequestReturnedError: "assistant-request-returned-error";
    readonly AssistantRequestReturnedUnspeakableError: "assistant-request-returned-unspeakable-error";
    readonly AssistantRequestReturnedInvalidAssistant: "assistant-request-returned-invalid-assistant";
    readonly AssistantRequestReturnedNoAssistant: "assistant-request-returned-no-assistant";
    readonly AssistantRequestReturnedForwardingPhoneNumber: "assistant-request-returned-forwarding-phone-number";
    readonly CallStartErrorGetOrg: "call.start.error-get-org";
    readonly CallStartErrorGetSubscription: "call.start.error-get-subscription";
    readonly CallStartErrorGetAssistant: "call.start.error-get-assistant";
    readonly CallStartErrorGetPhoneNumber: "call.start.error-get-phone-number";
    readonly CallStartErrorGetCustomer: "call.start.error-get-customer";
    readonly CallStartErrorGetResourcesValidation: "call.start.error-get-resources-validation";
    readonly CallStartErrorVapiNumberInternational: "call.start.error-vapi-number-international";
    readonly CallStartErrorVapiNumberOutboundDailyLimit: "call.start.error-vapi-number-outbound-daily-limit";
    readonly CallStartErrorGetTransport: "call.start.error-get-transport";
    readonly AssistantNotValid: "assistant-not-valid";
    readonly DatabaseError: "database-error";
    readonly AssistantNotFound: "assistant-not-found";
    readonly PipelineErrorOpenaiVoiceFailed: "pipeline-error-openai-voice-failed";
    readonly PipelineErrorCartesiaVoiceFailed: "pipeline-error-cartesia-voice-failed";
    readonly PipelineErrorDeepgramVoiceFailed: "pipeline-error-deepgram-voice-failed";
    readonly PipelineErrorElevenLabsVoiceFailed: "pipeline-error-eleven-labs-voice-failed";
    readonly PipelineErrorPlayhtVoiceFailed: "pipeline-error-playht-voice-failed";
    readonly PipelineErrorLmntVoiceFailed: "pipeline-error-lmnt-voice-failed";
    readonly PipelineErrorAzureVoiceFailed: "pipeline-error-azure-voice-failed";
    readonly PipelineErrorRimeAiVoiceFailed: "pipeline-error-rime-ai-voice-failed";
    readonly PipelineErrorSmallestAiVoiceFailed: "pipeline-error-smallest-ai-voice-failed";
    readonly PipelineErrorNeuphonicVoiceFailed: "pipeline-error-neuphonic-voice-failed";
    readonly PipelineErrorHumeVoiceFailed: "pipeline-error-hume-voice-failed";
    readonly PipelineErrorSesameVoiceFailed: "pipeline-error-sesame-voice-failed";
    readonly PipelineErrorTavusVideoFailed: "pipeline-error-tavus-video-failed";
    readonly CallInProgressErrorVapifaultOpenaiVoiceFailed: "call.in-progress.error-vapifault-openai-voice-failed";
    readonly CallInProgressErrorVapifaultCartesiaVoiceFailed: "call.in-progress.error-vapifault-cartesia-voice-failed";
    readonly CallInProgressErrorVapifaultDeepgramVoiceFailed: "call.in-progress.error-vapifault-deepgram-voice-failed";
    readonly CallInProgressErrorVapifaultElevenLabsVoiceFailed: "call.in-progress.error-vapifault-eleven-labs-voice-failed";
    readonly CallInProgressErrorVapifaultPlayhtVoiceFailed: "call.in-progress.error-vapifault-playht-voice-failed";
    readonly CallInProgressErrorVapifaultLmntVoiceFailed: "call.in-progress.error-vapifault-lmnt-voice-failed";
    readonly CallInProgressErrorVapifaultAzureVoiceFailed: "call.in-progress.error-vapifault-azure-voice-failed";
    readonly CallInProgressErrorVapifaultRimeAiVoiceFailed: "call.in-progress.error-vapifault-rime-ai-voice-failed";
    readonly CallInProgressErrorVapifaultSmallestAiVoiceFailed: "call.in-progress.error-vapifault-smallest-ai-voice-failed";
    readonly CallInProgressErrorVapifaultNeuphonicVoiceFailed: "call.in-progress.error-vapifault-neuphonic-voice-failed";
    readonly CallInProgressErrorVapifaultHumeVoiceFailed: "call.in-progress.error-vapifault-hume-voice-failed";
    readonly CallInProgressErrorVapifaultSesameVoiceFailed: "call.in-progress.error-vapifault-sesame-voice-failed";
    readonly CallInProgressErrorVapifaultTavusVideoFailed: "call.in-progress.error-vapifault-tavus-video-failed";
    readonly PipelineErrorVapiLlmFailed: "pipeline-error-vapi-llm-failed";
    readonly PipelineErrorVapi400BadRequestValidationFailed: "pipeline-error-vapi-400-bad-request-validation-failed";
    readonly PipelineErrorVapi401Unauthorized: "pipeline-error-vapi-401-unauthorized";
    readonly PipelineErrorVapi403ModelAccessDenied: "pipeline-error-vapi-403-model-access-denied";
    readonly PipelineErrorVapi429ExceededQuota: "pipeline-error-vapi-429-exceeded-quota";
    readonly PipelineErrorVapi500ServerError: "pipeline-error-vapi-500-server-error";
    readonly PipelineErrorVapi503ServerOverloadedError: "pipeline-error-vapi-503-server-overloaded-error";
    readonly CallInProgressErrorVapifaultVapiLlmFailed: "call.in-progress.error-vapifault-vapi-llm-failed";
    readonly CallInProgressErrorVapifaultVapi400BadRequestValidationFailed: "call.in-progress.error-vapifault-vapi-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultVapi401Unauthorized: "call.in-progress.error-vapifault-vapi-401-unauthorized";
    readonly CallInProgressErrorVapifaultVapi403ModelAccessDenied: "call.in-progress.error-vapifault-vapi-403-model-access-denied";
    readonly CallInProgressErrorVapifaultVapi429ExceededQuota: "call.in-progress.error-vapifault-vapi-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultVapi500ServerError: "call.in-progress.error-providerfault-vapi-500-server-error";
    readonly CallInProgressErrorProviderfaultVapi503ServerOverloadedError: "call.in-progress.error-providerfault-vapi-503-server-overloaded-error";
    readonly PipelineErrorDeepgramTranscriberFailed: "pipeline-error-deepgram-transcriber-failed";
    readonly CallInProgressErrorVapifaultDeepgramTranscriberFailed: "call.in-progress.error-vapifault-deepgram-transcriber-failed";
    readonly PipelineErrorGladiaTranscriberFailed: "pipeline-error-gladia-transcriber-failed";
    readonly CallInProgressErrorVapifaultGladiaTranscriberFailed: "call.in-progress.error-vapifault-gladia-transcriber-failed";
    readonly PipelineErrorSpeechmaticsTranscriberFailed: "pipeline-error-speechmatics-transcriber-failed";
    readonly CallInProgressErrorVapifaultSpeechmaticsTranscriberFailed: "call.in-progress.error-vapifault-speechmatics-transcriber-failed";
    readonly PipelineErrorAssemblyAiTranscriberFailed: "pipeline-error-assembly-ai-transcriber-failed";
    readonly PipelineErrorAssemblyAiReturning400InsufficentFunds: "pipeline-error-assembly-ai-returning-400-insufficent-funds";
    readonly PipelineErrorAssemblyAiReturning400PaidOnlyFeature: "pipeline-error-assembly-ai-returning-400-paid-only-feature";
    readonly PipelineErrorAssemblyAiReturning401InvalidCredentials: "pipeline-error-assembly-ai-returning-401-invalid-credentials";
    readonly PipelineErrorAssemblyAiReturning500InvalidSchema: "pipeline-error-assembly-ai-returning-500-invalid-schema";
    readonly PipelineErrorAssemblyAiReturning500WordBoostParsingFailed: "pipeline-error-assembly-ai-returning-500-word-boost-parsing-failed";
    readonly CallInProgressErrorVapifaultAssemblyAiTranscriberFailed: "call.in-progress.error-vapifault-assembly-ai-transcriber-failed";
    readonly CallInProgressErrorVapifaultAssemblyAiReturning400InsufficentFunds: "call.in-progress.error-vapifault-assembly-ai-returning-400-insufficent-funds";
    readonly CallInProgressErrorVapifaultAssemblyAiReturning400PaidOnlyFeature: "call.in-progress.error-vapifault-assembly-ai-returning-400-paid-only-feature";
    readonly CallInProgressErrorVapifaultAssemblyAiReturning401InvalidCredentials: "call.in-progress.error-vapifault-assembly-ai-returning-401-invalid-credentials";
    readonly CallInProgressErrorVapifaultAssemblyAiReturning500InvalidSchema: "call.in-progress.error-vapifault-assembly-ai-returning-500-invalid-schema";
    readonly CallInProgressErrorVapifaultAssemblyAiReturning500WordBoostParsingFailed: "call.in-progress.error-vapifault-assembly-ai-returning-500-word-boost-parsing-failed";
    readonly PipelineErrorTalkscriberTranscriberFailed: "pipeline-error-talkscriber-transcriber-failed";
    readonly CallInProgressErrorVapifaultTalkscriberTranscriberFailed: "call.in-progress.error-vapifault-talkscriber-transcriber-failed";
    readonly PipelineErrorAzureSpeechTranscriberFailed: "pipeline-error-azure-speech-transcriber-failed";
    readonly CallInProgressErrorVapifaultAzureSpeechTranscriberFailed: "call.in-progress.error-vapifault-azure-speech-transcriber-failed";
    readonly CallInProgressErrorPipelineNoAvailableLlmModel: "call.in-progress.error-pipeline-no-available-llm-model";
    readonly WorkerShutdown: "worker-shutdown";
    readonly UnknownError: "unknown-error";
    readonly VonageDisconnected: "vonage-disconnected";
    readonly VonageFailedToConnectCall: "vonage-failed-to-connect-call";
    readonly VonageCompleted: "vonage-completed";
    readonly PhoneCallProviderBypassEnabledButNoCallReceived: "phone-call-provider-bypass-enabled-but-no-call-received";
    readonly CallInProgressErrorVapifaultTransportNeverConnected: "call.in-progress.error-vapifault-transport-never-connected";
    readonly CallInProgressErrorVapifaultTransportConnectedButCallNotActive: "call.in-progress.error-vapifault-transport-connected-but-call-not-active";
    readonly CallInProgressErrorVapifaultCallStartedButConnectionToTransportMissing: "call.in-progress.error-vapifault-call-started-but-connection-to-transport-missing";
    readonly CallInProgressErrorVapifaultOpenaiLlmFailed: "call.in-progress.error-vapifault-openai-llm-failed";
    readonly CallInProgressErrorVapifaultAzureOpenaiLlmFailed: "call.in-progress.error-vapifault-azure-openai-llm-failed";
    readonly CallInProgressErrorVapifaultGroqLlmFailed: "call.in-progress.error-vapifault-groq-llm-failed";
    readonly CallInProgressErrorVapifaultGoogleLlmFailed: "call.in-progress.error-vapifault-google-llm-failed";
    readonly CallInProgressErrorVapifaultXaiLlmFailed: "call.in-progress.error-vapifault-xai-llm-failed";
    readonly CallInProgressErrorVapifaultMistralLlmFailed: "call.in-progress.error-vapifault-mistral-llm-failed";
    readonly CallInProgressErrorVapifaultInflectionAiLlmFailed: "call.in-progress.error-vapifault-inflection-ai-llm-failed";
    readonly CallInProgressErrorVapifaultCerebrasLlmFailed: "call.in-progress.error-vapifault-cerebras-llm-failed";
    readonly CallInProgressErrorVapifaultDeepSeekLlmFailed: "call.in-progress.error-vapifault-deep-seek-llm-failed";
    readonly PipelineErrorOpenai400BadRequestValidationFailed: "pipeline-error-openai-400-bad-request-validation-failed";
    readonly PipelineErrorOpenai401Unauthorized: "pipeline-error-openai-401-unauthorized";
    readonly PipelineErrorOpenai401IncorrectApiKey: "pipeline-error-openai-401-incorrect-api-key";
    readonly PipelineErrorOpenai401AccountNotInOrganization: "pipeline-error-openai-401-account-not-in-organization";
    readonly PipelineErrorOpenai403ModelAccessDenied: "pipeline-error-openai-403-model-access-denied";
    readonly PipelineErrorOpenai429ExceededQuota: "pipeline-error-openai-429-exceeded-quota";
    readonly PipelineErrorOpenai429RateLimitReached: "pipeline-error-openai-429-rate-limit-reached";
    readonly PipelineErrorOpenai500ServerError: "pipeline-error-openai-500-server-error";
    readonly PipelineErrorOpenai503ServerOverloadedError: "pipeline-error-openai-503-server-overloaded-error";
    readonly PipelineErrorOpenaiLlmFailed: "pipeline-error-openai-llm-failed";
    readonly CallInProgressErrorVapifaultOpenai400BadRequestValidationFailed: "call.in-progress.error-vapifault-openai-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultOpenai401Unauthorized: "call.in-progress.error-vapifault-openai-401-unauthorized";
    readonly CallInProgressErrorVapifaultOpenai401IncorrectApiKey: "call.in-progress.error-vapifault-openai-401-incorrect-api-key";
    readonly CallInProgressErrorVapifaultOpenai401AccountNotInOrganization: "call.in-progress.error-vapifault-openai-401-account-not-in-organization";
    readonly CallInProgressErrorVapifaultOpenai403ModelAccessDenied: "call.in-progress.error-vapifault-openai-403-model-access-denied";
    readonly CallInProgressErrorVapifaultOpenai429ExceededQuota: "call.in-progress.error-vapifault-openai-429-exceeded-quota";
    readonly CallInProgressErrorVapifaultOpenai429RateLimitReached: "call.in-progress.error-vapifault-openai-429-rate-limit-reached";
    readonly CallInProgressErrorProviderfaultOpenai500ServerError: "call.in-progress.error-providerfault-openai-500-server-error";
    readonly CallInProgressErrorProviderfaultOpenai503ServerOverloadedError: "call.in-progress.error-providerfault-openai-503-server-overloaded-error";
    readonly PipelineErrorAzureOpenai400BadRequestValidationFailed: "pipeline-error-azure-openai-400-bad-request-validation-failed";
    readonly PipelineErrorAzureOpenai401Unauthorized: "pipeline-error-azure-openai-401-unauthorized";
    readonly PipelineErrorAzureOpenai403ModelAccessDenied: "pipeline-error-azure-openai-403-model-access-denied";
    readonly PipelineErrorAzureOpenai429ExceededQuota: "pipeline-error-azure-openai-429-exceeded-quota";
    readonly PipelineErrorAzureOpenai500ServerError: "pipeline-error-azure-openai-500-server-error";
    readonly PipelineErrorAzureOpenai503ServerOverloadedError: "pipeline-error-azure-openai-503-server-overloaded-error";
    readonly PipelineErrorAzureOpenaiLlmFailed: "pipeline-error-azure-openai-llm-failed";
    readonly CallInProgressErrorVapifaultAzureOpenai400BadRequestValidationFailed: "call.in-progress.error-vapifault-azure-openai-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultAzureOpenai401Unauthorized: "call.in-progress.error-vapifault-azure-openai-401-unauthorized";
    readonly CallInProgressErrorVapifaultAzureOpenai403ModelAccessDenied: "call.in-progress.error-vapifault-azure-openai-403-model-access-denied";
    readonly CallInProgressErrorVapifaultAzureOpenai429ExceededQuota: "call.in-progress.error-vapifault-azure-openai-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultAzureOpenai500ServerError: "call.in-progress.error-providerfault-azure-openai-500-server-error";
    readonly CallInProgressErrorProviderfaultAzureOpenai503ServerOverloadedError: "call.in-progress.error-providerfault-azure-openai-503-server-overloaded-error";
    readonly PipelineErrorGoogle400BadRequestValidationFailed: "pipeline-error-google-400-bad-request-validation-failed";
    readonly PipelineErrorGoogle401Unauthorized: "pipeline-error-google-401-unauthorized";
    readonly PipelineErrorGoogle403ModelAccessDenied: "pipeline-error-google-403-model-access-denied";
    readonly PipelineErrorGoogle429ExceededQuota: "pipeline-error-google-429-exceeded-quota";
    readonly PipelineErrorGoogle500ServerError: "pipeline-error-google-500-server-error";
    readonly PipelineErrorGoogle503ServerOverloadedError: "pipeline-error-google-503-server-overloaded-error";
    readonly PipelineErrorGoogleLlmFailed: "pipeline-error-google-llm-failed";
    readonly CallInProgressErrorVapifaultGoogle400BadRequestValidationFailed: "call.in-progress.error-vapifault-google-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultGoogle401Unauthorized: "call.in-progress.error-vapifault-google-401-unauthorized";
    readonly CallInProgressErrorVapifaultGoogle403ModelAccessDenied: "call.in-progress.error-vapifault-google-403-model-access-denied";
    readonly CallInProgressErrorVapifaultGoogle429ExceededQuota: "call.in-progress.error-vapifault-google-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultGoogle500ServerError: "call.in-progress.error-providerfault-google-500-server-error";
    readonly CallInProgressErrorProviderfaultGoogle503ServerOverloadedError: "call.in-progress.error-providerfault-google-503-server-overloaded-error";
    readonly PipelineErrorXai400BadRequestValidationFailed: "pipeline-error-xai-400-bad-request-validation-failed";
    readonly PipelineErrorXai401Unauthorized: "pipeline-error-xai-401-unauthorized";
    readonly PipelineErrorXai403ModelAccessDenied: "pipeline-error-xai-403-model-access-denied";
    readonly PipelineErrorXai429ExceededQuota: "pipeline-error-xai-429-exceeded-quota";
    readonly PipelineErrorXai500ServerError: "pipeline-error-xai-500-server-error";
    readonly PipelineErrorXai503ServerOverloadedError: "pipeline-error-xai-503-server-overloaded-error";
    readonly PipelineErrorXaiLlmFailed: "pipeline-error-xai-llm-failed";
    readonly CallInProgressErrorVapifaultXai400BadRequestValidationFailed: "call.in-progress.error-vapifault-xai-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultXai401Unauthorized: "call.in-progress.error-vapifault-xai-401-unauthorized";
    readonly CallInProgressErrorVapifaultXai403ModelAccessDenied: "call.in-progress.error-vapifault-xai-403-model-access-denied";
    readonly CallInProgressErrorVapifaultXai429ExceededQuota: "call.in-progress.error-vapifault-xai-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultXai500ServerError: "call.in-progress.error-providerfault-xai-500-server-error";
    readonly CallInProgressErrorProviderfaultXai503ServerOverloadedError: "call.in-progress.error-providerfault-xai-503-server-overloaded-error";
    readonly PipelineErrorMistral400BadRequestValidationFailed: "pipeline-error-mistral-400-bad-request-validation-failed";
    readonly PipelineErrorMistral401Unauthorized: "pipeline-error-mistral-401-unauthorized";
    readonly PipelineErrorMistral403ModelAccessDenied: "pipeline-error-mistral-403-model-access-denied";
    readonly PipelineErrorMistral429ExceededQuota: "pipeline-error-mistral-429-exceeded-quota";
    readonly PipelineErrorMistral500ServerError: "pipeline-error-mistral-500-server-error";
    readonly PipelineErrorMistral503ServerOverloadedError: "pipeline-error-mistral-503-server-overloaded-error";
    readonly PipelineErrorMistralLlmFailed: "pipeline-error-mistral-llm-failed";
    readonly CallInProgressErrorVapifaultMistral400BadRequestValidationFailed: "call.in-progress.error-vapifault-mistral-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultMistral401Unauthorized: "call.in-progress.error-vapifault-mistral-401-unauthorized";
    readonly CallInProgressErrorVapifaultMistral403ModelAccessDenied: "call.in-progress.error-vapifault-mistral-403-model-access-denied";
    readonly CallInProgressErrorVapifaultMistral429ExceededQuota: "call.in-progress.error-vapifault-mistral-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultMistral500ServerError: "call.in-progress.error-providerfault-mistral-500-server-error";
    readonly CallInProgressErrorProviderfaultMistral503ServerOverloadedError: "call.in-progress.error-providerfault-mistral-503-server-overloaded-error";
    readonly PipelineErrorInflectionAi400BadRequestValidationFailed: "pipeline-error-inflection-ai-400-bad-request-validation-failed";
    readonly PipelineErrorInflectionAi401Unauthorized: "pipeline-error-inflection-ai-401-unauthorized";
    readonly PipelineErrorInflectionAi403ModelAccessDenied: "pipeline-error-inflection-ai-403-model-access-denied";
    readonly PipelineErrorInflectionAi429ExceededQuota: "pipeline-error-inflection-ai-429-exceeded-quota";
    readonly PipelineErrorInflectionAi500ServerError: "pipeline-error-inflection-ai-500-server-error";
    readonly PipelineErrorInflectionAi503ServerOverloadedError: "pipeline-error-inflection-ai-503-server-overloaded-error";
    readonly PipelineErrorInflectionAiLlmFailed: "pipeline-error-inflection-ai-llm-failed";
    readonly CallInProgressErrorVapifaultInflectionAi400BadRequestValidationFailed: "call.in-progress.error-vapifault-inflection-ai-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultInflectionAi401Unauthorized: "call.in-progress.error-vapifault-inflection-ai-401-unauthorized";
    readonly CallInProgressErrorVapifaultInflectionAi403ModelAccessDenied: "call.in-progress.error-vapifault-inflection-ai-403-model-access-denied";
    readonly CallInProgressErrorVapifaultInflectionAi429ExceededQuota: "call.in-progress.error-vapifault-inflection-ai-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultInflectionAi500ServerError: "call.in-progress.error-providerfault-inflection-ai-500-server-error";
    readonly CallInProgressErrorProviderfaultInflectionAi503ServerOverloadedError: "call.in-progress.error-providerfault-inflection-ai-503-server-overloaded-error";
    readonly PipelineErrorDeepSeek400BadRequestValidationFailed: "pipeline-error-deep-seek-400-bad-request-validation-failed";
    readonly PipelineErrorDeepSeek401Unauthorized: "pipeline-error-deep-seek-401-unauthorized";
    readonly PipelineErrorDeepSeek403ModelAccessDenied: "pipeline-error-deep-seek-403-model-access-denied";
    readonly PipelineErrorDeepSeek429ExceededQuota: "pipeline-error-deep-seek-429-exceeded-quota";
    readonly PipelineErrorDeepSeek500ServerError: "pipeline-error-deep-seek-500-server-error";
    readonly PipelineErrorDeepSeek503ServerOverloadedError: "pipeline-error-deep-seek-503-server-overloaded-error";
    readonly PipelineErrorDeepSeekLlmFailed: "pipeline-error-deep-seek-llm-failed";
    readonly CallInProgressErrorVapifaultDeepSeek400BadRequestValidationFailed: "call.in-progress.error-vapifault-deep-seek-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultDeepSeek401Unauthorized: "call.in-progress.error-vapifault-deep-seek-401-unauthorized";
    readonly CallInProgressErrorVapifaultDeepSeek403ModelAccessDenied: "call.in-progress.error-vapifault-deep-seek-403-model-access-denied";
    readonly CallInProgressErrorVapifaultDeepSeek429ExceededQuota: "call.in-progress.error-vapifault-deep-seek-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultDeepSeek500ServerError: "call.in-progress.error-providerfault-deep-seek-500-server-error";
    readonly CallInProgressErrorProviderfaultDeepSeek503ServerOverloadedError: "call.in-progress.error-providerfault-deep-seek-503-server-overloaded-error";
    readonly PipelineErrorGroq400BadRequestValidationFailed: "pipeline-error-groq-400-bad-request-validation-failed";
    readonly PipelineErrorGroq401Unauthorized: "pipeline-error-groq-401-unauthorized";
    readonly PipelineErrorGroq403ModelAccessDenied: "pipeline-error-groq-403-model-access-denied";
    readonly PipelineErrorGroq429ExceededQuota: "pipeline-error-groq-429-exceeded-quota";
    readonly PipelineErrorGroq500ServerError: "pipeline-error-groq-500-server-error";
    readonly PipelineErrorGroq503ServerOverloadedError: "pipeline-error-groq-503-server-overloaded-error";
    readonly PipelineErrorGroqLlmFailed: "pipeline-error-groq-llm-failed";
    readonly CallInProgressErrorVapifaultGroq400BadRequestValidationFailed: "call.in-progress.error-vapifault-groq-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultGroq401Unauthorized: "call.in-progress.error-vapifault-groq-401-unauthorized";
    readonly CallInProgressErrorVapifaultGroq403ModelAccessDenied: "call.in-progress.error-vapifault-groq-403-model-access-denied";
    readonly CallInProgressErrorVapifaultGroq429ExceededQuota: "call.in-progress.error-vapifault-groq-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultGroq500ServerError: "call.in-progress.error-providerfault-groq-500-server-error";
    readonly CallInProgressErrorProviderfaultGroq503ServerOverloadedError: "call.in-progress.error-providerfault-groq-503-server-overloaded-error";
    readonly PipelineErrorCerebras400BadRequestValidationFailed: "pipeline-error-cerebras-400-bad-request-validation-failed";
    readonly PipelineErrorCerebras401Unauthorized: "pipeline-error-cerebras-401-unauthorized";
    readonly PipelineErrorCerebras403ModelAccessDenied: "pipeline-error-cerebras-403-model-access-denied";
    readonly PipelineErrorCerebras429ExceededQuota: "pipeline-error-cerebras-429-exceeded-quota";
    readonly PipelineErrorCerebras500ServerError: "pipeline-error-cerebras-500-server-error";
    readonly PipelineErrorCerebras503ServerOverloadedError: "pipeline-error-cerebras-503-server-overloaded-error";
    readonly PipelineErrorCerebrasLlmFailed: "pipeline-error-cerebras-llm-failed";
    readonly CallInProgressErrorVapifaultCerebras400BadRequestValidationFailed: "call.in-progress.error-vapifault-cerebras-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultCerebras401Unauthorized: "call.in-progress.error-vapifault-cerebras-401-unauthorized";
    readonly CallInProgressErrorVapifaultCerebras403ModelAccessDenied: "call.in-progress.error-vapifault-cerebras-403-model-access-denied";
    readonly CallInProgressErrorVapifaultCerebras429ExceededQuota: "call.in-progress.error-vapifault-cerebras-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultCerebras500ServerError: "call.in-progress.error-providerfault-cerebras-500-server-error";
    readonly CallInProgressErrorProviderfaultCerebras503ServerOverloadedError: "call.in-progress.error-providerfault-cerebras-503-server-overloaded-error";
    readonly PipelineErrorAnthropic400BadRequestValidationFailed: "pipeline-error-anthropic-400-bad-request-validation-failed";
    readonly PipelineErrorAnthropic401Unauthorized: "pipeline-error-anthropic-401-unauthorized";
    readonly PipelineErrorAnthropic403ModelAccessDenied: "pipeline-error-anthropic-403-model-access-denied";
    readonly PipelineErrorAnthropic429ExceededQuota: "pipeline-error-anthropic-429-exceeded-quota";
    readonly PipelineErrorAnthropic500ServerError: "pipeline-error-anthropic-500-server-error";
    readonly PipelineErrorAnthropic503ServerOverloadedError: "pipeline-error-anthropic-503-server-overloaded-error";
    readonly PipelineErrorAnthropicLlmFailed: "pipeline-error-anthropic-llm-failed";
    readonly CallInProgressErrorVapifaultAnthropicLlmFailed: "call.in-progress.error-vapifault-anthropic-llm-failed";
    readonly CallInProgressErrorVapifaultAnthropic400BadRequestValidationFailed: "call.in-progress.error-vapifault-anthropic-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultAnthropic401Unauthorized: "call.in-progress.error-vapifault-anthropic-401-unauthorized";
    readonly CallInProgressErrorVapifaultAnthropic403ModelAccessDenied: "call.in-progress.error-vapifault-anthropic-403-model-access-denied";
    readonly CallInProgressErrorVapifaultAnthropic429ExceededQuota: "call.in-progress.error-vapifault-anthropic-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultAnthropic500ServerError: "call.in-progress.error-providerfault-anthropic-500-server-error";
    readonly CallInProgressErrorProviderfaultAnthropic503ServerOverloadedError: "call.in-progress.error-providerfault-anthropic-503-server-overloaded-error";
    readonly PipelineErrorTogetherAi400BadRequestValidationFailed: "pipeline-error-together-ai-400-bad-request-validation-failed";
    readonly PipelineErrorTogetherAi401Unauthorized: "pipeline-error-together-ai-401-unauthorized";
    readonly PipelineErrorTogetherAi403ModelAccessDenied: "pipeline-error-together-ai-403-model-access-denied";
    readonly PipelineErrorTogetherAi429ExceededQuota: "pipeline-error-together-ai-429-exceeded-quota";
    readonly PipelineErrorTogetherAi500ServerError: "pipeline-error-together-ai-500-server-error";
    readonly PipelineErrorTogetherAi503ServerOverloadedError: "pipeline-error-together-ai-503-server-overloaded-error";
    readonly PipelineErrorTogetherAiLlmFailed: "pipeline-error-together-ai-llm-failed";
    readonly CallInProgressErrorVapifaultTogetherAiLlmFailed: "call.in-progress.error-vapifault-together-ai-llm-failed";
    readonly CallInProgressErrorVapifaultTogetherAi400BadRequestValidationFailed: "call.in-progress.error-vapifault-together-ai-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultTogetherAi401Unauthorized: "call.in-progress.error-vapifault-together-ai-401-unauthorized";
    readonly CallInProgressErrorVapifaultTogetherAi403ModelAccessDenied: "call.in-progress.error-vapifault-together-ai-403-model-access-denied";
    readonly CallInProgressErrorVapifaultTogetherAi429ExceededQuota: "call.in-progress.error-vapifault-together-ai-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultTogetherAi500ServerError: "call.in-progress.error-providerfault-together-ai-500-server-error";
    readonly CallInProgressErrorProviderfaultTogetherAi503ServerOverloadedError: "call.in-progress.error-providerfault-together-ai-503-server-overloaded-error";
    readonly PipelineErrorAnyscale400BadRequestValidationFailed: "pipeline-error-anyscale-400-bad-request-validation-failed";
    readonly PipelineErrorAnyscale401Unauthorized: "pipeline-error-anyscale-401-unauthorized";
    readonly PipelineErrorAnyscale403ModelAccessDenied: "pipeline-error-anyscale-403-model-access-denied";
    readonly PipelineErrorAnyscale429ExceededQuota: "pipeline-error-anyscale-429-exceeded-quota";
    readonly PipelineErrorAnyscale500ServerError: "pipeline-error-anyscale-500-server-error";
    readonly PipelineErrorAnyscale503ServerOverloadedError: "pipeline-error-anyscale-503-server-overloaded-error";
    readonly PipelineErrorAnyscaleLlmFailed: "pipeline-error-anyscale-llm-failed";
    readonly CallInProgressErrorVapifaultAnyscaleLlmFailed: "call.in-progress.error-vapifault-anyscale-llm-failed";
    readonly CallInProgressErrorVapifaultAnyscale400BadRequestValidationFailed: "call.in-progress.error-vapifault-anyscale-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultAnyscale401Unauthorized: "call.in-progress.error-vapifault-anyscale-401-unauthorized";
    readonly CallInProgressErrorVapifaultAnyscale403ModelAccessDenied: "call.in-progress.error-vapifault-anyscale-403-model-access-denied";
    readonly CallInProgressErrorVapifaultAnyscale429ExceededQuota: "call.in-progress.error-vapifault-anyscale-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultAnyscale500ServerError: "call.in-progress.error-providerfault-anyscale-500-server-error";
    readonly CallInProgressErrorProviderfaultAnyscale503ServerOverloadedError: "call.in-progress.error-providerfault-anyscale-503-server-overloaded-error";
    readonly PipelineErrorOpenrouter400BadRequestValidationFailed: "pipeline-error-openrouter-400-bad-request-validation-failed";
    readonly PipelineErrorOpenrouter401Unauthorized: "pipeline-error-openrouter-401-unauthorized";
    readonly PipelineErrorOpenrouter403ModelAccessDenied: "pipeline-error-openrouter-403-model-access-denied";
    readonly PipelineErrorOpenrouter429ExceededQuota: "pipeline-error-openrouter-429-exceeded-quota";
    readonly PipelineErrorOpenrouter500ServerError: "pipeline-error-openrouter-500-server-error";
    readonly PipelineErrorOpenrouter503ServerOverloadedError: "pipeline-error-openrouter-503-server-overloaded-error";
    readonly PipelineErrorOpenrouterLlmFailed: "pipeline-error-openrouter-llm-failed";
    readonly CallInProgressErrorVapifaultOpenrouterLlmFailed: "call.in-progress.error-vapifault-openrouter-llm-failed";
    readonly CallInProgressErrorVapifaultOpenrouter400BadRequestValidationFailed: "call.in-progress.error-vapifault-openrouter-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultOpenrouter401Unauthorized: "call.in-progress.error-vapifault-openrouter-401-unauthorized";
    readonly CallInProgressErrorVapifaultOpenrouter403ModelAccessDenied: "call.in-progress.error-vapifault-openrouter-403-model-access-denied";
    readonly CallInProgressErrorVapifaultOpenrouter429ExceededQuota: "call.in-progress.error-vapifault-openrouter-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultOpenrouter500ServerError: "call.in-progress.error-providerfault-openrouter-500-server-error";
    readonly CallInProgressErrorProviderfaultOpenrouter503ServerOverloadedError: "call.in-progress.error-providerfault-openrouter-503-server-overloaded-error";
    readonly PipelineErrorPerplexityAi400BadRequestValidationFailed: "pipeline-error-perplexity-ai-400-bad-request-validation-failed";
    readonly PipelineErrorPerplexityAi401Unauthorized: "pipeline-error-perplexity-ai-401-unauthorized";
    readonly PipelineErrorPerplexityAi403ModelAccessDenied: "pipeline-error-perplexity-ai-403-model-access-denied";
    readonly PipelineErrorPerplexityAi429ExceededQuota: "pipeline-error-perplexity-ai-429-exceeded-quota";
    readonly PipelineErrorPerplexityAi500ServerError: "pipeline-error-perplexity-ai-500-server-error";
    readonly PipelineErrorPerplexityAi503ServerOverloadedError: "pipeline-error-perplexity-ai-503-server-overloaded-error";
    readonly PipelineErrorPerplexityAiLlmFailed: "pipeline-error-perplexity-ai-llm-failed";
    readonly CallInProgressErrorVapifaultPerplexityAiLlmFailed: "call.in-progress.error-vapifault-perplexity-ai-llm-failed";
    readonly CallInProgressErrorVapifaultPerplexityAi400BadRequestValidationFailed: "call.in-progress.error-vapifault-perplexity-ai-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultPerplexityAi401Unauthorized: "call.in-progress.error-vapifault-perplexity-ai-401-unauthorized";
    readonly CallInProgressErrorVapifaultPerplexityAi403ModelAccessDenied: "call.in-progress.error-vapifault-perplexity-ai-403-model-access-denied";
    readonly CallInProgressErrorVapifaultPerplexityAi429ExceededQuota: "call.in-progress.error-vapifault-perplexity-ai-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultPerplexityAi500ServerError: "call.in-progress.error-providerfault-perplexity-ai-500-server-error";
    readonly CallInProgressErrorProviderfaultPerplexityAi503ServerOverloadedError: "call.in-progress.error-providerfault-perplexity-ai-503-server-overloaded-error";
    readonly PipelineErrorDeepinfra400BadRequestValidationFailed: "pipeline-error-deepinfra-400-bad-request-validation-failed";
    readonly PipelineErrorDeepinfra401Unauthorized: "pipeline-error-deepinfra-401-unauthorized";
    readonly PipelineErrorDeepinfra403ModelAccessDenied: "pipeline-error-deepinfra-403-model-access-denied";
    readonly PipelineErrorDeepinfra429ExceededQuota: "pipeline-error-deepinfra-429-exceeded-quota";
    readonly PipelineErrorDeepinfra500ServerError: "pipeline-error-deepinfra-500-server-error";
    readonly PipelineErrorDeepinfra503ServerOverloadedError: "pipeline-error-deepinfra-503-server-overloaded-error";
    readonly PipelineErrorDeepinfraLlmFailed: "pipeline-error-deepinfra-llm-failed";
    readonly CallInProgressErrorVapifaultDeepinfraLlmFailed: "call.in-progress.error-vapifault-deepinfra-llm-failed";
    readonly CallInProgressErrorVapifaultDeepinfra400BadRequestValidationFailed: "call.in-progress.error-vapifault-deepinfra-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultDeepinfra401Unauthorized: "call.in-progress.error-vapifault-deepinfra-401-unauthorized";
    readonly CallInProgressErrorVapifaultDeepinfra403ModelAccessDenied: "call.in-progress.error-vapifault-deepinfra-403-model-access-denied";
    readonly CallInProgressErrorVapifaultDeepinfra429ExceededQuota: "call.in-progress.error-vapifault-deepinfra-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultDeepinfra500ServerError: "call.in-progress.error-providerfault-deepinfra-500-server-error";
    readonly CallInProgressErrorProviderfaultDeepinfra503ServerOverloadedError: "call.in-progress.error-providerfault-deepinfra-503-server-overloaded-error";
    readonly PipelineErrorRunpod400BadRequestValidationFailed: "pipeline-error-runpod-400-bad-request-validation-failed";
    readonly PipelineErrorRunpod401Unauthorized: "pipeline-error-runpod-401-unauthorized";
    readonly PipelineErrorRunpod403ModelAccessDenied: "pipeline-error-runpod-403-model-access-denied";
    readonly PipelineErrorRunpod429ExceededQuota: "pipeline-error-runpod-429-exceeded-quota";
    readonly PipelineErrorRunpod500ServerError: "pipeline-error-runpod-500-server-error";
    readonly PipelineErrorRunpod503ServerOverloadedError: "pipeline-error-runpod-503-server-overloaded-error";
    readonly PipelineErrorRunpodLlmFailed: "pipeline-error-runpod-llm-failed";
    readonly CallInProgressErrorVapifaultRunpodLlmFailed: "call.in-progress.error-vapifault-runpod-llm-failed";
    readonly CallInProgressErrorVapifaultRunpod400BadRequestValidationFailed: "call.in-progress.error-vapifault-runpod-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultRunpod401Unauthorized: "call.in-progress.error-vapifault-runpod-401-unauthorized";
    readonly CallInProgressErrorVapifaultRunpod403ModelAccessDenied: "call.in-progress.error-vapifault-runpod-403-model-access-denied";
    readonly CallInProgressErrorVapifaultRunpod429ExceededQuota: "call.in-progress.error-vapifault-runpod-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultRunpod500ServerError: "call.in-progress.error-providerfault-runpod-500-server-error";
    readonly CallInProgressErrorProviderfaultRunpod503ServerOverloadedError: "call.in-progress.error-providerfault-runpod-503-server-overloaded-error";
    readonly PipelineErrorCustomLlm400BadRequestValidationFailed: "pipeline-error-custom-llm-400-bad-request-validation-failed";
    readonly PipelineErrorCustomLlm401Unauthorized: "pipeline-error-custom-llm-401-unauthorized";
    readonly PipelineErrorCustomLlm403ModelAccessDenied: "pipeline-error-custom-llm-403-model-access-denied";
    readonly PipelineErrorCustomLlm429ExceededQuota: "pipeline-error-custom-llm-429-exceeded-quota";
    readonly PipelineErrorCustomLlm500ServerError: "pipeline-error-custom-llm-500-server-error";
    readonly PipelineErrorCustomLlm503ServerOverloadedError: "pipeline-error-custom-llm-503-server-overloaded-error";
    readonly PipelineErrorCustomLlmLlmFailed: "pipeline-error-custom-llm-llm-failed";
    readonly CallInProgressErrorVapifaultCustomLlmLlmFailed: "call.in-progress.error-vapifault-custom-llm-llm-failed";
    readonly CallInProgressErrorVapifaultCustomLlm400BadRequestValidationFailed: "call.in-progress.error-vapifault-custom-llm-400-bad-request-validation-failed";
    readonly CallInProgressErrorVapifaultCustomLlm401Unauthorized: "call.in-progress.error-vapifault-custom-llm-401-unauthorized";
    readonly CallInProgressErrorVapifaultCustomLlm403ModelAccessDenied: "call.in-progress.error-vapifault-custom-llm-403-model-access-denied";
    readonly CallInProgressErrorVapifaultCustomLlm429ExceededQuota: "call.in-progress.error-vapifault-custom-llm-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultCustomLlm500ServerError: "call.in-progress.error-providerfault-custom-llm-500-server-error";
    readonly CallInProgressErrorProviderfaultCustomLlm503ServerOverloadedError: "call.in-progress.error-providerfault-custom-llm-503-server-overloaded-error";
    readonly PipelineErrorCustomVoiceFailed: "pipeline-error-custom-voice-failed";
    readonly PipelineErrorCartesiaSocketHangUp: "pipeline-error-cartesia-socket-hang-up";
    readonly PipelineErrorCartesiaRequestedPayment: "pipeline-error-cartesia-requested-payment";
    readonly PipelineErrorCartesia500ServerError: "pipeline-error-cartesia-500-server-error";
    readonly PipelineErrorCartesia503ServerError: "pipeline-error-cartesia-503-server-error";
    readonly PipelineErrorCartesia522ServerError: "pipeline-error-cartesia-522-server-error";
    readonly CallInProgressErrorVapifaultCartesiaSocketHangUp: "call.in-progress.error-vapifault-cartesia-socket-hang-up";
    readonly CallInProgressErrorVapifaultCartesiaRequestedPayment: "call.in-progress.error-vapifault-cartesia-requested-payment";
    readonly CallInProgressErrorProviderfaultCartesia500ServerError: "call.in-progress.error-providerfault-cartesia-500-server-error";
    readonly CallInProgressErrorProviderfaultCartesia503ServerError: "call.in-progress.error-providerfault-cartesia-503-server-error";
    readonly CallInProgressErrorProviderfaultCartesia522ServerError: "call.in-progress.error-providerfault-cartesia-522-server-error";
    readonly PipelineErrorElevenLabsVoiceNotFound: "pipeline-error-eleven-labs-voice-not-found";
    readonly PipelineErrorElevenLabsQuotaExceeded: "pipeline-error-eleven-labs-quota-exceeded";
    readonly PipelineErrorElevenLabsUnauthorizedAccess: "pipeline-error-eleven-labs-unauthorized-access";
    readonly PipelineErrorElevenLabsUnauthorizedToAccessModel: "pipeline-error-eleven-labs-unauthorized-to-access-model";
    readonly PipelineErrorElevenLabsProfessionalVoicesOnlyForCreatorPlus: "pipeline-error-eleven-labs-professional-voices-only-for-creator-plus";
    readonly PipelineErrorElevenLabsBlockedFreePlanAndRequestedUpgrade: "pipeline-error-eleven-labs-blocked-free-plan-and-requested-upgrade";
    readonly PipelineErrorElevenLabsBlockedConcurrentRequestsAndRequestedUpgrade: "pipeline-error-eleven-labs-blocked-concurrent-requests-and-requested-upgrade";
    readonly PipelineErrorElevenLabsBlockedUsingInstantVoiceCloneAndRequestedUpgrade: "pipeline-error-eleven-labs-blocked-using-instant-voice-clone-and-requested-upgrade";
    readonly PipelineErrorElevenLabsSystemBusyAndRequestedUpgrade: "pipeline-error-eleven-labs-system-busy-and-requested-upgrade";
    readonly PipelineErrorElevenLabsVoiceNotFineTuned: "pipeline-error-eleven-labs-voice-not-fine-tuned";
    readonly PipelineErrorElevenLabsInvalidApiKey: "pipeline-error-eleven-labs-invalid-api-key";
    readonly PipelineErrorElevenLabsInvalidVoiceSamples: "pipeline-error-eleven-labs-invalid-voice-samples";
    readonly PipelineErrorElevenLabsVoiceDisabledByOwner: "pipeline-error-eleven-labs-voice-disabled-by-owner";
    readonly PipelineErrorElevenLabsBlockedAccountInProbation: "pipeline-error-eleven-labs-blocked-account-in-probation";
    readonly PipelineErrorElevenLabsBlockedContentAgainstTheirPolicy: "pipeline-error-eleven-labs-blocked-content-against-their-policy";
    readonly PipelineErrorElevenLabsMissingSamplesForVoiceClone: "pipeline-error-eleven-labs-missing-samples-for-voice-clone";
    readonly PipelineErrorElevenLabsVoiceNotFineTunedAndCannotBeUsed: "pipeline-error-eleven-labs-voice-not-fine-tuned-and-cannot-be-used";
    readonly PipelineErrorElevenLabsVoiceNotAllowedForFreeUsers: "pipeline-error-eleven-labs-voice-not-allowed-for-free-users";
    readonly PipelineErrorElevenLabsMaxCharacterLimitExceeded: "pipeline-error-eleven-labs-max-character-limit-exceeded";
    readonly PipelineErrorElevenLabsBlockedVoicePotentiallyAgainstTermsOfServiceAndAwaitingVerification: "pipeline-error-eleven-labs-blocked-voice-potentially-against-terms-of-service-and-awaiting-verification";
    readonly PipelineErrorElevenLabs500ServerError: "pipeline-error-eleven-labs-500-server-error";
    readonly CallInProgressErrorVapifaultElevenLabsVoiceNotFound: "call.in-progress.error-vapifault-eleven-labs-voice-not-found";
    readonly CallInProgressErrorVapifaultElevenLabsQuotaExceeded: "call.in-progress.error-vapifault-eleven-labs-quota-exceeded";
    readonly CallInProgressErrorVapifaultElevenLabsUnauthorizedAccess: "call.in-progress.error-vapifault-eleven-labs-unauthorized-access";
    readonly CallInProgressErrorVapifaultElevenLabsUnauthorizedToAccessModel: "call.in-progress.error-vapifault-eleven-labs-unauthorized-to-access-model";
    readonly CallInProgressErrorVapifaultElevenLabsProfessionalVoicesOnlyForCreatorPlus: "call.in-progress.error-vapifault-eleven-labs-professional-voices-only-for-creator-plus";
    readonly CallInProgressErrorVapifaultElevenLabsBlockedFreePlanAndRequestedUpgrade: "call.in-progress.error-vapifault-eleven-labs-blocked-free-plan-and-requested-upgrade";
    readonly CallInProgressErrorVapifaultElevenLabsBlockedConcurrentRequestsAndRequestedUpgrade: "call.in-progress.error-vapifault-eleven-labs-blocked-concurrent-requests-and-requested-upgrade";
    readonly CallInProgressErrorVapifaultElevenLabsBlockedUsingInstantVoiceCloneAndRequestedUpgrade: "call.in-progress.error-vapifault-eleven-labs-blocked-using-instant-voice-clone-and-requested-upgrade";
    readonly CallInProgressErrorVapifaultElevenLabsSystemBusyAndRequestedUpgrade: "call.in-progress.error-vapifault-eleven-labs-system-busy-and-requested-upgrade";
    readonly CallInProgressErrorVapifaultElevenLabsVoiceNotFineTuned: "call.in-progress.error-vapifault-eleven-labs-voice-not-fine-tuned";
    readonly CallInProgressErrorVapifaultElevenLabsInvalidApiKey: "call.in-progress.error-vapifault-eleven-labs-invalid-api-key";
    readonly CallInProgressErrorVapifaultElevenLabsInvalidVoiceSamples: "call.in-progress.error-vapifault-eleven-labs-invalid-voice-samples";
    readonly CallInProgressErrorVapifaultElevenLabsVoiceDisabledByOwner: "call.in-progress.error-vapifault-eleven-labs-voice-disabled-by-owner";
    readonly CallInProgressErrorVapifaultElevenLabsBlockedAccountInProbation: "call.in-progress.error-vapifault-eleven-labs-blocked-account-in-probation";
    readonly CallInProgressErrorVapifaultElevenLabsBlockedContentAgainstTheirPolicy: "call.in-progress.error-vapifault-eleven-labs-blocked-content-against-their-policy";
    readonly CallInProgressErrorVapifaultElevenLabsMissingSamplesForVoiceClone: "call.in-progress.error-vapifault-eleven-labs-missing-samples-for-voice-clone";
    readonly CallInProgressErrorVapifaultElevenLabsVoiceNotFineTunedAndCannotBeUsed: "call.in-progress.error-vapifault-eleven-labs-voice-not-fine-tuned-and-cannot-be-used";
    readonly CallInProgressErrorVapifaultElevenLabsVoiceNotAllowedForFreeUsers: "call.in-progress.error-vapifault-eleven-labs-voice-not-allowed-for-free-users";
    readonly CallInProgressErrorVapifaultElevenLabsMaxCharacterLimitExceeded: "call.in-progress.error-vapifault-eleven-labs-max-character-limit-exceeded";
    readonly CallInProgressErrorVapifaultElevenLabsBlockedVoicePotentiallyAgainstTermsOfServiceAndAwaitingVerification: "call.in-progress.error-vapifault-eleven-labs-blocked-voice-potentially-against-terms-of-service-and-awaiting-verification";
    readonly CallInProgressErrorProviderfaultElevenLabs500ServerError: "call.in-progress.error-providerfault-eleven-labs-500-server-error";
    readonly PipelineErrorPlayhtRequestTimedOut: "pipeline-error-playht-request-timed-out";
    readonly PipelineErrorPlayhtInvalidVoice: "pipeline-error-playht-invalid-voice";
    readonly PipelineErrorPlayhtUnexpectedError: "pipeline-error-playht-unexpected-error";
    readonly PipelineErrorPlayhtOutOfCredits: "pipeline-error-playht-out-of-credits";
    readonly PipelineErrorPlayhtInvalidEmotion: "pipeline-error-playht-invalid-emotion";
    readonly PipelineErrorPlayhtVoiceMustBeAValidVoiceManifestUri: "pipeline-error-playht-voice-must-be-a-valid-voice-manifest-uri";
    readonly PipelineErrorPlayht401Unauthorized: "pipeline-error-playht-401-unauthorized";
    readonly PipelineErrorPlayht403ForbiddenOutOfCharacters: "pipeline-error-playht-403-forbidden-out-of-characters";
    readonly PipelineErrorPlayht403ForbiddenApiAccessNotAvailable: "pipeline-error-playht-403-forbidden-api-access-not-available";
    readonly PipelineErrorPlayht429ExceededQuota: "pipeline-error-playht-429-exceeded-quota";
    readonly PipelineErrorPlayht502GatewayError: "pipeline-error-playht-502-gateway-error";
    readonly PipelineErrorPlayht504GatewayError: "pipeline-error-playht-504-gateway-error";
    readonly CallInProgressErrorVapifaultPlayhtRequestTimedOut: "call.in-progress.error-vapifault-playht-request-timed-out";
    readonly CallInProgressErrorVapifaultPlayhtInvalidVoice: "call.in-progress.error-vapifault-playht-invalid-voice";
    readonly CallInProgressErrorVapifaultPlayhtUnexpectedError: "call.in-progress.error-vapifault-playht-unexpected-error";
    readonly CallInProgressErrorVapifaultPlayhtOutOfCredits: "call.in-progress.error-vapifault-playht-out-of-credits";
    readonly CallInProgressErrorVapifaultPlayhtInvalidEmotion: "call.in-progress.error-vapifault-playht-invalid-emotion";
    readonly CallInProgressErrorVapifaultPlayhtVoiceMustBeAValidVoiceManifestUri: "call.in-progress.error-vapifault-playht-voice-must-be-a-valid-voice-manifest-uri";
    readonly CallInProgressErrorVapifaultPlayht401Unauthorized: "call.in-progress.error-vapifault-playht-401-unauthorized";
    readonly CallInProgressErrorVapifaultPlayht403ForbiddenOutOfCharacters: "call.in-progress.error-vapifault-playht-403-forbidden-out-of-characters";
    readonly CallInProgressErrorVapifaultPlayht403ForbiddenApiAccessNotAvailable: "call.in-progress.error-vapifault-playht-403-forbidden-api-access-not-available";
    readonly CallInProgressErrorVapifaultPlayht429ExceededQuota: "call.in-progress.error-vapifault-playht-429-exceeded-quota";
    readonly CallInProgressErrorProviderfaultPlayht502GatewayError: "call.in-progress.error-providerfault-playht-502-gateway-error";
    readonly CallInProgressErrorProviderfaultPlayht504GatewayError: "call.in-progress.error-providerfault-playht-504-gateway-error";
    readonly PipelineErrorCustomTranscriberFailed: "pipeline-error-custom-transcriber-failed";
    readonly CallInProgressErrorVapifaultCustomTranscriberFailed: "call.in-progress.error-vapifault-custom-transcriber-failed";
    readonly PipelineError11LabsTranscriberFailed: "pipeline-error-11labs-transcriber-failed";
    readonly CallInProgressErrorVapifault11LabsTranscriberFailed: "call.in-progress.error-vapifault-11labs-transcriber-failed";
    readonly PipelineErrorDeepgramReturning400NoSuchModelLanguageTierCombination: "pipeline-error-deepgram-returning-400-no-such-model-language-tier-combination";
    readonly PipelineErrorDeepgramReturning401InvalidCredentials: "pipeline-error-deepgram-returning-401-invalid-credentials";
    readonly PipelineErrorDeepgramReturning403ModelAccessDenied: "pipeline-error-deepgram-returning-403-model-access-denied";
    readonly PipelineErrorDeepgramReturning404NotFound: "pipeline-error-deepgram-returning-404-not-found";
    readonly PipelineErrorDeepgramReturning500InvalidJson: "pipeline-error-deepgram-returning-500-invalid-json";
    readonly PipelineErrorDeepgramReturning502NetworkError: "pipeline-error-deepgram-returning-502-network-error";
    readonly PipelineErrorDeepgramReturning502BadGatewayEhostunreach: "pipeline-error-deepgram-returning-502-bad-gateway-ehostunreach";
    readonly CallInProgressErrorVapifaultDeepgramReturning400NoSuchModelLanguageTierCombination: "call.in-progress.error-vapifault-deepgram-returning-400-no-such-model-language-tier-combination";
    readonly CallInProgressErrorVapifaultDeepgramReturning401InvalidCredentials: "call.in-progress.error-vapifault-deepgram-returning-401-invalid-credentials";
    readonly CallInProgressErrorVapifaultDeepgramReturning404NotFound: "call.in-progress.error-vapifault-deepgram-returning-404-not-found";
    readonly CallInProgressErrorVapifaultDeepgramReturning403ModelAccessDenied: "call.in-progress.error-vapifault-deepgram-returning-403-model-access-denied";
    readonly CallInProgressErrorProviderfaultDeepgramReturning500InvalidJson: "call.in-progress.error-providerfault-deepgram-returning-500-invalid-json";
    readonly CallInProgressErrorProviderfaultDeepgramReturning502NetworkError: "call.in-progress.error-providerfault-deepgram-returning-502-network-error";
    readonly CallInProgressErrorProviderfaultDeepgramReturning502BadGatewayEhostunreach: "call.in-progress.error-providerfault-deepgram-returning-502-bad-gateway-ehostunreach";
    readonly PipelineErrorGoogleTranscriberFailed: "pipeline-error-google-transcriber-failed";
    readonly CallInProgressErrorVapifaultGoogleTranscriberFailed: "call.in-progress.error-vapifault-google-transcriber-failed";
    readonly PipelineErrorOpenaiTranscriberFailed: "pipeline-error-openai-transcriber-failed";
    readonly CallInProgressErrorVapifaultOpenaiTranscriberFailed: "call.in-progress.error-vapifault-openai-transcriber-failed";
    readonly AssistantEndedCall: "assistant-ended-call";
    readonly AssistantSaidEndCallPhrase: "assistant-said-end-call-phrase";
    readonly AssistantEndedCallWithHangupTask: "assistant-ended-call-with-hangup-task";
    readonly AssistantEndedCallAfterMessageSpoken: "assistant-ended-call-after-message-spoken";
    readonly AssistantForwardedCall: "assistant-forwarded-call";
    readonly AssistantJoinTimedOut: "assistant-join-timed-out";
    readonly CallInProgressErrorAssistantDidNotReceiveCustomerAudio: "call.in-progress.error-assistant-did-not-receive-customer-audio";
    readonly CustomerBusy: "customer-busy";
    readonly CustomerEndedCall: "customer-ended-call";
    readonly CustomerDidNotAnswer: "customer-did-not-answer";
    readonly CustomerDidNotGiveMicrophonePermission: "customer-did-not-give-microphone-permission";
    readonly ExceededMaxDuration: "exceeded-max-duration";
    readonly ManuallyCanceled: "manually-canceled";
    readonly PhoneCallProviderClosedWebsocket: "phone-call-provider-closed-websocket";
    readonly SilenceTimedOut: "silence-timed-out";
    readonly CallInProgressErrorSipTelephonyProviderFailedToConnectCall: "call.in-progress.error-sip-telephony-provider-failed-to-connect-call";
    readonly TwilioFailedToConnectCall: "twilio-failed-to-connect-call";
    readonly TwilioReportedCustomerMisdialed: "twilio-reported-customer-misdialed";
    readonly VonageRejected: "vonage-rejected";
    readonly Voicemail: "voicemail";
};
