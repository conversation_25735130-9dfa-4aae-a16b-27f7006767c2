<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Environment Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Simple Environment Test</h1>
        <p>This test shows exactly what environment variables are available and what values the tests will use.</p>
        
        <div id="output" class="output">Loading...</div>
        
        <button onclick="runTest()">🔄 Run Test Again</button>
        <button onclick="testVapiCall()">📞 Test Vapi Call</button>
    </div>

    <script type="module">
        function runTest() {
            const outputDiv = document.getElementById('output');
            let output = '🔍 ENVIRONMENT TEST RESULTS\n';
            output += '=' .repeat(50) + '\n\n';
            
            // Test 1: Check if import.meta.env exists
            output += '1. Environment Object Check:\n';
            output += `   import.meta.env exists: ${typeof import.meta.env !== 'undefined'}\n`;
            output += `   import.meta.env type: ${typeof import.meta.env}\n`;
            
            if (import.meta.env) {
                const allKeys = Object.keys(import.meta.env);
                output += `   Total env vars: ${allKeys.length}\n`;
                output += `   All keys: ${allKeys.join(', ')}\n\n`;
            } else {
                output += '   ❌ import.meta.env is not available!\n\n';
            }
            
            // Test 2: Check specific Vapi variables
            output += '2. Vapi Environment Variables:\n';
            const vapiVars = [
                'VITE_VAPI_PUBLIC_KEY',
                'VITE_VAPI_SECRET_KEY', 
                'VITE_VAPI_ASSISTANT_ID',
                'VITE_VAPI_BASE_URL',
                'VITE_VAPI_DEFAULT_VOICE_PROVIDER',
                'VITE_VAPI_DEFAULT_VOICE_ID'
            ];
            
            vapiVars.forEach(varName => {
                const value = import.meta.env?.[varName];
                if (value) {
                    const displayValue = varName.includes('KEY') ? 
                        value.substring(0, 8) + '...' : value;
                    output += `   ✅ ${varName}: ${displayValue}\n`;
                } else {
                    output += `   ❌ ${varName}: Not set (${value})\n`;
                }
            });
            
            // Test 3: Show actual values that tests will use
            output += '\n3. Actual Values Used by Tests (with fallbacks):\n';
            const actualPublicKey = import.meta.env?.VITE_VAPI_PUBLIC_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
            const actualSecretKey = import.meta.env?.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
            const actualAssistantId = import.meta.env?.VITE_VAPI_ASSISTANT_ID || 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
            
            output += `   Public Key: ${actualPublicKey.substring(0, 8)}...\n`;
            output += `   Secret Key: ${actualSecretKey.substring(0, 8)}...\n`;
            output += `   Assistant ID: ${actualAssistantId}\n`;
            
            // Test 4: Check if keys are from env or fallback
            output += '\n4. Source Check:\n';
            output += `   Public Key source: ${import.meta.env?.VITE_VAPI_PUBLIC_KEY ? 'Environment' : 'Fallback'}\n`;
            output += `   Secret Key source: ${import.meta.env?.VITE_VAPI_SECRET_KEY ? 'Environment' : 'Fallback'}\n`;
            output += `   Assistant ID source: ${import.meta.env?.VITE_VAPI_ASSISTANT_ID ? 'Environment' : 'Fallback'}\n`;
            
            // Test 5: Environment info
            output += '\n5. Environment Info:\n';
            output += `   NODE_ENV: ${import.meta.env?.NODE_ENV || 'Not set'}\n`;
            output += `   MODE: ${import.meta.env?.MODE || 'Not set'}\n`;
            output += `   DEV: ${import.meta.env?.DEV || 'Not set'}\n`;
            output += `   PROD: ${import.meta.env?.PROD || 'Not set'}\n`;
            
            output += '\n' + '=' .repeat(50) + '\n';
            output += 'Test completed at: ' + new Date().toLocaleString();
            
            outputDiv.textContent = output;
            console.log(output);
        }
        
        async function testVapiCall() {
            const outputDiv = document.getElementById('output');
            let output = '📞 VAPI CALL TEST\n';
            output += '=' .repeat(50) + '\n\n';
            
            try {
                // Get the actual values that will be used
                const publicKey = import.meta.env?.VITE_VAPI_PUBLIC_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
                const assistantId = import.meta.env?.VITE_VAPI_ASSISTANT_ID || 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
                
                output += `Using Public Key: ${publicKey.substring(0, 8)}...\n`;
                output += `Using Assistant ID: ${assistantId}\n\n`;
                
                // Try to load Vapi
                output += 'Loading Vapi SDK...\n';
                const { loadVapiSDK, createVapiInstance } = await import('/src/utils/vapiLoader.js');
                
                const VapiClass = await loadVapiSDK();
                output += `✅ Vapi SDK loaded: ${typeof VapiClass}\n`;
                
                // Try to create instance
                output += 'Creating Vapi instance...\n';
                const vapi = await createVapiInstance(publicKey);
                output += `✅ Vapi instance created: ${!!vapi}\n`;
                output += `✅ Has start method: ${typeof vapi?.start === 'function'}\n`;
                
                // Set up event listeners
                if (vapi && typeof vapi.on === 'function') {
                    vapi.on('call-start', () => {
                        output += '✅ Call started successfully!\n';
                        outputDiv.textContent = output;
                    });
                    
                    vapi.on('call-end', (data) => {
                        output += `📞 Call ended: ${JSON.stringify(data)}\n`;
                        outputDiv.textContent = output;
                    });
                    
                    vapi.on('error', (error) => {
                        output += `❌ Error: ${JSON.stringify(error)}\n`;
                        outputDiv.textContent = output;
                    });
                    
                    output += 'Event listeners set up\n';
                    output += 'Attempting to start call...\n';
                    
                    // Try to start call
                    const result = await vapi.start(assistantId);
                    output += `✅ Call start result: ${JSON.stringify(result)}\n`;
                    
                    // Stop after 5 seconds
                    setTimeout(() => {
                        if (vapi && typeof vapi.stop === 'function') {
                            vapi.stop();
                            output += '⏹️ Call stopped\n';
                            outputDiv.textContent = output;
                        }
                    }, 5000);
                }
                
            } catch (error) {
                output += `❌ Test failed: ${error.message}\n`;
                output += `❌ Stack: ${error.stack}\n`;
            }
            
            output += '\n' + '=' .repeat(50) + '\n';
            output += 'Test completed at: ' + new Date().toLocaleString();
            
            outputDiv.textContent = output;
            console.log(output);
        }
        
        // Make functions available globally
        window.runTest = runTest;
        window.testVapiCall = testVapiCall;
        
        // Auto-run test on load
        setTimeout(runTest, 500);
    </script>
</body>
</html>
