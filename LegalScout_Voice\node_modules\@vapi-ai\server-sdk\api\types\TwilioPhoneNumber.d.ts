/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface TwilioPhoneNumber {
    /**
     * This is the fallback destination an inbound call will be transferred to if:
     * 1. `assistantId` is not set
     * 2. `squadId` is not set
     * 3. and, `assistant-request` message to the `serverUrl` fails
     *
     * If this is not set and above conditions are met, the inbound call is hung up with an error message.
     */
    fallbackDestination?: Vapi.TwilioPhoneNumberFallbackDestination;
    provider: "twilio";
    /** This is the unique identifier for the phone number. */
    id: string;
    /** This is the unique identifier for the org that this phone number belongs to. */
    orgId: string;
    /** This is the ISO 8601 date-time string of when the phone number was created. */
    createdAt: string;
    /** This is the ISO 8601 date-time string of when the phone number was last updated. */
    updatedAt: string;
    /** This is the status of the phone number. */
    status?: Vapi.TwilioPhoneNumberStatus;
    /** This is the name of the phone number. This is just for your own reference. */
    name?: string;
    /**
     * This is the assistant that will be used for incoming calls to this phone number.
     *
     * If neither `assistantId` nor `squadId` is set, `assistant-request` will be sent to your Server URL. Check `ServerMessage` and `ServerMessageResponse` for the shape of the message and response that is expected.
     */
    assistantId?: string;
    /**
     * This is the squad that will be used for incoming calls to this phone number.
     *
     * If neither `assistantId` nor `squadId` is set, `assistant-request` will be sent to your Server URL. Check `ServerMessage` and `ServerMessageResponse` for the shape of the message and response that is expected.
     */
    squadId?: string;
    /**
     * This is where Vapi will send webhooks. You can find all webhooks available along with their shape in ServerMessage schema.
     *
     * The order of precedence is:
     *
     * 1. assistant.server
     * 2. phoneNumber.server
     * 3. org.server
     */
    server?: Vapi.Server;
    /** These are the digits of the phone number you own on your Twilio. */
    number: string;
    /** This is the Twilio Account SID for the phone number. */
    twilioAccountSid: string;
    /** This is the Twilio Auth Token for the phone number. */
    twilioAuthToken: string;
}
