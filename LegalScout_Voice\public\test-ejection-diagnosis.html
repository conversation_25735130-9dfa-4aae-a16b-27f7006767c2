<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LegalScout Voice Assistant - Ejection Diagnosis</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            color: #34495e;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #2980b9; }
        button:disabled { background: #bdc3c7; cursor: not-allowed; }
        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .config-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
        .call-controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }
        .event-log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 LegalScout Voice Assistant - Ejection Diagnosis</h1>
        
        <div class="test-section">
            <h3>🎯 Ejection Error Test Suite</h3>
            <p>This tool specifically diagnoses the "Meeting ended due to ejection: Meeting has ended" error.</p>
            <div class="call-controls">
                <button onclick="testBasicCall()">Test Basic Call</button>
                <button onclick="testWithMinimalConfig()">Test Minimal Config</button>
                <button onclick="testWithFullConfig()">Test Full Config</button>
                <button onclick="testAssistantConfig()">Test Assistant Config</button>
                <button onclick="testEjectionCauses()">Test Ejection Causes</button>
                <button onclick="clearLogs()">Clear Logs</button>
            </div>
        </div>

        <div class="grid">
            <div class="test-section">
                <h3>📊 Call Status</h3>
                <div id="call-status" class="status info">Ready to test</div>
                <div id="call-details"></div>
            </div>
            
            <div class="test-section">
                <h3>🔧 Current Configuration</h3>
                <div id="config-display" class="config-display"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 Event Log</h3>
            <div id="event-log" class="event-log"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Detailed Logs</h3>
            <div id="logs" class="log-area"></div>
        </div>
    </div>

    <script>
        let logs = [];
        let eventLogs = [];
        let currentVapi = null;
        let callStartTime = null;

        // Configuration from your environment
        const CONFIG = {
            apiKey: '6734febc-fc65-4669-93b0-929b31ff6564',
            assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
            baseUrl: 'https://api.vapi.ai'
        };

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logsDiv = document.getElementById('logs');
            logsDiv.textContent = logs.join('\n');
            logsDiv.scrollTop = logsDiv.scrollHeight;
            
            console.log(logEntry);
        }

        function logEvent(event, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const eventEntry = `[${timestamp}] EVENT: ${event}${data ? ' - ' + JSON.stringify(data) : ''}`;
            eventLogs.push(eventEntry);
            
            const eventLogDiv = document.getElementById('event-log');
            eventLogDiv.textContent = eventLogs.join('\n');
            eventLogDiv.scrollTop = eventLogDiv.scrollHeight;
        }

        function updateCallStatus(status, details = '') {
            const statusDiv = document.getElementById('call-status');
            const detailsDiv = document.getElementById('call-details');
            
            statusDiv.textContent = status;
            statusDiv.className = 'status ' + (
                status.includes('Error') || status.includes('Failed') ? 'error' :
                status.includes('Connected') || status.includes('Success') ? 'success' :
                status.includes('Connecting') ? 'warning' : 'info'
            );
            
            detailsDiv.innerHTML = details;
        }

        function updateConfigDisplay(config) {
            const configDiv = document.getElementById('config-display');
            configDiv.textContent = JSON.stringify(config, null, 2);
        }

        function clearLogs() {
            logs = [];
            eventLogs = [];
            document.getElementById('logs').textContent = '';
            document.getElementById('event-log').textContent = '';
            updateCallStatus('Ready to test');
        }

        async function loadVapiSDK() {
            return new Promise((resolve, reject) => {
                if (window.Vapi) {
                    resolve();
                    return;
                }

                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/vapi.js';
                script.onload = () => {
                    log('✅ Vapi SDK loaded successfully');
                    resolve();
                };
                script.onerror = () => {
                    log('❌ Failed to load Vapi SDK');
                    reject(new Error('Failed to load Vapi SDK'));
                };
                document.head.appendChild(script);
            });
        }

        function setupVapiEventListeners(vapi) {
            // Clear previous event logs
            eventLogs = [];

            vapi.on('call-start', () => {
                logEvent('call-start');
                updateCallStatus('Connected', 'Call started successfully');
                callStartTime = Date.now();
            });

            vapi.on('call-end', (data) => {
                logEvent('call-end', data);
                const duration = callStartTime ? (Date.now() - callStartTime) / 1000 : 0;
                updateCallStatus('Ended', `Call duration: ${duration.toFixed(1)}s`);
                callStartTime = null;
            });

            vapi.on('error', (error) => {
                logEvent('error', error);
                log(`❌ Vapi Error: ${JSON.stringify(error)}`);
                updateCallStatus('Error', `Error: ${error.message || JSON.stringify(error)}`);
            });

            vapi.on('speech-start', () => {
                logEvent('speech-start');
            });

            vapi.on('speech-end', () => {
                logEvent('speech-end');
            });

            vapi.on('volume-level', (level) => {
                // Don't log every volume level, just track it
            });

            vapi.on('message', (message) => {
                logEvent('message', { type: message.type });
                if (message.type === 'transcript') {
                    log(`📝 Transcript: ${message.transcript}`);
                }
            });

            // Listen for any other events
            const originalOn = vapi.on.bind(vapi);
            vapi.on = function(event, callback) {
                const wrappedCallback = function(...args) {
                    logEvent(`custom-${event}`, args[0]);
                    return callback.apply(this, args);
                };
                return originalOn(event, wrappedCallback);
            };
        }

        async function testBasicCall() {
            log('🚀 Starting Basic Call Test...');
            updateCallStatus('Initializing...');

            try {
                await loadVapiSDK();
                
                log('Creating Vapi instance with minimal configuration...');
                currentVapi = new window.Vapi(CONFIG.apiKey);
                
                setupVapiEventListeners(currentVapi);
                
                const basicConfig = {
                    assistantId: CONFIG.assistantId
                };
                
                updateConfigDisplay(basicConfig);
                
                log('Starting call with basic configuration...');
                updateCallStatus('Connecting...', 'Using minimal configuration');
                
                await currentVapi.start(CONFIG.assistantId);
                
                log('✅ Basic call started successfully');
                
            } catch (error) {
                log(`❌ Basic call failed: ${error.message}`);
                updateCallStatus('Failed', error.message);
            }
        }

        async function testWithMinimalConfig() {
            log('🚀 Starting Minimal Config Test...');
            updateCallStatus('Initializing...');

            try {
                await loadVapiSDK();
                
                log('Creating Vapi instance...');
                currentVapi = new window.Vapi(CONFIG.apiKey);
                
                setupVapiEventListeners(currentVapi);
                
                // Minimal configuration to avoid ejection
                const minimalConfig = {
                    assistantId: CONFIG.assistantId,
                    // No overrides at all
                };
                
                updateConfigDisplay(minimalConfig);
                
                log('Starting call with minimal configuration (no overrides)...');
                updateCallStatus('Connecting...', 'No assistant overrides');
                
                // Use the simplest possible call
                await currentVapi.start(CONFIG.assistantId);
                
                log('✅ Minimal config call started successfully');
                
            } catch (error) {
                log(`❌ Minimal config call failed: ${error.message}`);
                updateCallStatus('Failed', error.message);
            }
        }

        async function testWithFullConfig() {
            log('🚀 Starting Full Config Test...');
            updateCallStatus('Initializing...');

            try {
                await loadVapiSDK();
                
                log('Creating Vapi instance...');
                currentVapi = new window.Vapi(CONFIG.apiKey);
                
                setupVapiEventListeners(currentVapi);
                
                // Full configuration that might cause ejection
                const fullConfig = {
                    assistantId: CONFIG.assistantId,
                    assistantOverrides: {
                        firstMessage: "Hello! I'm Scout from LegalScout. How can I help you today?",
                        voice: {
                            provider: "openai",
                            voiceId: "echo"
                        },
                        recordingEnabled: true
                    }
                };
                
                updateConfigDisplay(fullConfig);
                
                log('Starting call with full configuration...');
                updateCallStatus('Connecting...', 'With assistant overrides');
                
                await currentVapi.start(fullConfig);
                
                log('✅ Full config call started successfully');
                
            } catch (error) {
                log(`❌ Full config call failed: ${error.message}`);
                updateCallStatus('Failed', error.message);
            }
        }

        async function testAssistantConfig() {
            log('🚀 Testing Assistant Configuration...');
            updateCallStatus('Checking assistant...');

            try {
                log('Fetching assistant configuration from Vapi API...');
                
                const response = await fetch(`${CONFIG.baseUrl}/assistant/${CONFIG.assistantId}`, {
                    headers: {
                        'Authorization': `Bearer ${CONFIG.apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const assistant = await response.json();
                
                log('✅ Assistant configuration retrieved');
                log(`Assistant Name: ${assistant.name}`);
                log(`Voice Provider: ${assistant.voice?.provider || 'Not set'}`);
                log(`Voice ID: ${assistant.voice?.voiceId || 'Not set'}`);
                log(`First Message: ${assistant.firstMessage || 'Not set'}`);
                
                updateConfigDisplay({
                    assistantId: CONFIG.assistantId,
                    name: assistant.name,
                    voice: assistant.voice,
                    firstMessage: assistant.firstMessage,
                    model: assistant.model
                });
                
                updateCallStatus('Assistant OK', `Voice: ${assistant.voice?.provider}/${assistant.voice?.voiceId}`);
                
                // Now try a call with this assistant
                log('Testing call with retrieved assistant configuration...');
                await loadVapiSDK();
                
                currentVapi = new window.Vapi(CONFIG.apiKey);
                setupVapiEventListeners(currentVapi);
                
                updateCallStatus('Connecting...', 'Using assistant as-is');
                await currentVapi.start(CONFIG.assistantId);
                
                log('✅ Assistant config call started successfully');
                
            } catch (error) {
                log(`❌ Assistant config test failed: ${error.message}`);
                updateCallStatus('Failed', error.message);
            }
        }

        function stopCall() {
            if (currentVapi) {
                log('Stopping current call...');
                currentVapi.stop();
                currentVapi = null;
                updateCallStatus('Stopped', 'Call stopped by user');
            }
        }

        // Add stop button
        document.addEventListener('DOMContentLoaded', () => {
            const controlsDiv = document.querySelector('.call-controls');
            const stopButton = document.createElement('button');
            stopButton.textContent = 'Stop Call';
            stopButton.onclick = stopCall;
            stopButton.style.backgroundColor = '#e74c3c';
            controlsDiv.appendChild(stopButton);
        });

        // Monitor for ejection errors specifically
        function monitorForEjectionErrors() {
            // Override console.error to catch ejection messages
            const originalError = console.error;
            console.error = function(...args) {
                const message = args.join(' ');
                if (message.includes('ejection') || message.includes('Meeting has ended')) {
                    logEvent('EJECTION-ERROR', message);
                    log(`🚨 EJECTION ERROR DETECTED: ${message}`);
                    updateCallStatus('Ejected', `Ejection detected: ${message}`);
                }
                return originalError.apply(console, args);
            };

            // Also monitor for specific Vapi error patterns
            window.addEventListener('error', (event) => {
                if (event.message && (event.message.includes('ejection') || event.message.includes('Meeting has ended'))) {
                    logEvent('WINDOW-ERROR', event.message);
                    log(`🚨 WINDOW ERROR: ${event.message}`);
                }
            });
        }

        // Common ejection causes to test
        async function testEjectionCauses() {
            log('🔍 Testing Common Ejection Causes...');

            const causes = [
                {
                    name: 'Invalid Assistant Overrides',
                    test: async () => {
                        const vapi = new window.Vapi(CONFIG.apiKey);
                        setupVapiEventListeners(vapi);
                        await vapi.start({
                            assistantId: CONFIG.assistantId,
                            assistantOverrides: {
                                invalidField: 'this should cause issues'
                            }
                        });
                    }
                },
                {
                    name: 'Conflicting Voice Configuration',
                    test: async () => {
                        const vapi = new window.Vapi(CONFIG.apiKey);
                        setupVapiEventListeners(vapi);
                        await vapi.start({
                            assistantId: CONFIG.assistantId,
                            assistantOverrides: {
                                voice: {
                                    provider: 'invalid-provider',
                                    voiceId: 'invalid-voice'
                                }
                            }
                        });
                    }
                },
                {
                    name: 'Multiple Simultaneous Calls',
                    test: async () => {
                        const vapi1 = new window.Vapi(CONFIG.apiKey);
                        const vapi2 = new window.Vapi(CONFIG.apiKey);
                        setupVapiEventListeners(vapi1);
                        setupVapiEventListeners(vapi2);
                        await Promise.all([
                            vapi1.start(CONFIG.assistantId),
                            vapi2.start(CONFIG.assistantId)
                        ]);
                    }
                }
            ];

            for (const cause of causes) {
                try {
                    log(`Testing: ${cause.name}...`);
                    await cause.test();
                    log(`✅ ${cause.name}: No immediate ejection`);
                } catch (error) {
                    log(`❌ ${cause.name}: ${error.message}`);
                }

                // Wait between tests
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        // Initialize
        monitorForEjectionErrors();
        log('🔍 LegalScout Voice Assistant Ejection Diagnosis Tool Ready');
        log('This tool will help identify why calls are being ejected');
        log('Monitoring for ejection errors in console and events...');
        updateConfigDisplay(CONFIG);
    </script>
</body>
</html>
