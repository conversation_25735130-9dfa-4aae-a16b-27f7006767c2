/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as Vapi from "../index";
export interface TavusVoice {
    /** This is the voice provider that will be used. */
    provider: "tavus";
    /** This is the provider-specific ID that will be used. */
    voiceId: Vapi.TavusVoiceVoiceId;
    /** This is the plan for chunking the model output before it is sent to the voice provider. */
    chunkPlan?: Vapi.ChunkPlan;
    /** This is the unique identifier for the persona that the replica will use in the conversation. */
    personaId?: string;
    /** This is the url that will receive webhooks with updates regarding the conversation state. */
    callbackUrl?: string;
    /** This is the name for the conversation. */
    conversationName?: string;
    /** This is the context that will be appended to any context provided in the persona, if one is provided. */
    conversationalContext?: string;
    /** This is the custom greeting that the replica will give once a participant joines the conversation. */
    customGreeting?: string;
    /** These are optional properties used to customize the conversation. */
    properties?: Vapi.TavusConversationProperties;
    /** This is the plan for voice provider fallbacks in the event that the primary voice provider fails. */
    fallbackPlan?: Vapi.FallbackPlan;
}
