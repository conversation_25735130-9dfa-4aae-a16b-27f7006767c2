/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface TimeRange {
    /**
     * This is the time step for aggregations.
     *
     * If not provided, defaults to returning for the entire time range.
     */
    step?: Vapi.TimeRangeStep;
    /**
     * This is the start date for the time range.
     *
     * If not provided, defaults to the 7 days ago.
     */
    start?: string;
    /**
     * This is the end date for the time range.
     *
     * If not provided, defaults to now.
     */
    end?: string;
    /**
     * This is the timezone you want to set for the query.
     *
     * If not provided, defaults to UTC.
     */
    timezone?: string;
}
