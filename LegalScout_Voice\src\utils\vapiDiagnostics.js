/**
 * Voice Assistant Diagnostics Utility
 *
 * This utility provides diagnostic functions for voice assistant integration.
 */

// Import direct API utilities
import { getApiKey } from './vapiDirectApi';

// Constants
const VAPI_API_URL = 'https://api.vapi.ai/v1';

/**
 * Test direct connection to voice assistant API
 * @returns {Promise<Object>} Test results
 */
export const testVapiDirectConnection = async () => {
  try {
    console.log('[vapiDiagnostics] Testing direct connection to voice assistant API');

    // Get API key
    const apiKey = getApiKey();
    if (!apiKey) {
      return {
        success: false,
        error: 'No API key available',
        details: 'Could not find voice assistant API key in environment variables or localStorage'
      };
    }

    // Test connection with a simple API call
    const response = await fetch(`${VAPI_API_URL}/assistants?limit=1`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: 'Successfully connected to Vapi API',
        assistantCount: data.length || 0
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API error: ${response.status} ${response.statusText}`,
        details: errorText
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Connection error: ${error.message}`,
      details: error.stack
    };
  }
};

/**
 * Get assistant by ID using direct API
 * @param {string} assistantId - Assistant ID
 * @returns {Promise<Object>} Test results
 */
export const testGetAssistant = async (assistantId) => {
  try {
    console.log(`[vapiDiagnostics] Testing get assistant: ${assistantId}`);

    if (!assistantId) {
      return {
        success: false,
        error: 'No assistant ID provided'
      };
    }

    // Get API key
    const apiKey = getApiKey();
    if (!apiKey) {
      return {
        success: false,
        error: 'No API key available',
        details: 'Could not find Vapi API key in environment variables or localStorage'
      };
    }

    // Test connection with a simple API call
    const response = await fetch(`${VAPI_API_URL}/assistants/${assistantId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: `Successfully retrieved assistant: ${data.name}`,
        assistant: data
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        error: `API error: ${response.status} ${response.statusText}`,
        details: errorText
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Connection error: ${error.message}`,
      details: error.stack
    };
  }
};

/**
 * Get environment configuration for voice assistant
 * @returns {Object} Environment configuration
 */
export const getVapiEnvironmentConfig = () => {
  return {
    publicKey: import.meta.env.VITE_VAPI_PUBLIC_KEY || 'Not set',
    secretKey: import.meta.env.VITE_VAPI_SECRET_KEY ? 'Set (hidden)' : 'Not set',
    forceMcpMode: false, // Updated in initAttorneyProfileManager.js to prioritize direct API
    localStorageKey: localStorage.getItem('vapi_api_key') ? 'Set (hidden)' : 'Not set'
  };
};

/**
 * Comprehensive Vapi diagnostic function to identify ejection issues
 * @returns {Promise<Object>} Diagnostic results
 */
export const diagnoseVapiEjectionIssue = async () => {
  const results = {
    timestamp: new Date().toISOString(),
    issues: [],
    recommendations: [],
    apiKeyStatus: 'unknown',
    assistantStatus: 'unknown',
    networkStatus: 'unknown',
    audioStatus: 'unknown'
  };

  console.log('🔍 [VapiDiagnostics] Starting comprehensive Vapi ejection diagnosis...');

  // 1. Check API Key Configuration
  try {
    const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
    const secretKey = import.meta.env.VITE_VAPI_SECRET_KEY;

    if (!publicKey) {
      results.issues.push('Missing VITE_VAPI_PUBLIC_KEY environment variable');
      results.recommendations.push('Set VITE_VAPI_PUBLIC_KEY in your .env file');
      results.apiKeyStatus = 'missing';
    } else if (publicKey.length !== 36) {
      results.issues.push('Invalid VITE_VAPI_PUBLIC_KEY format (should be UUID format)');
      results.recommendations.push('Verify your Vapi public key is correct');
      results.apiKeyStatus = 'invalid';
    } else {
      results.apiKeyStatus = 'valid';
      console.log('✅ [VapiDiagnostics] API key format is valid');
    }

    // Check if we're accidentally using assistant ID as API key
    if (publicKey === '310f0d43-27c2-47a5-a76d-e55171d024f7') {
      results.issues.push('Using assistant ID as API key - this will cause ejection');
      results.recommendations.push('Replace VITE_VAPI_PUBLIC_KEY with your actual Vapi public key');
      results.apiKeyStatus = 'assistant_id_used';
    }
  } catch (error) {
    results.issues.push(`Error checking API key: ${error.message}`);
    results.apiKeyStatus = 'error';
  }

  // 2. Test Assistant Accessibility
  try {
    const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
    const publicKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;

    if (publicKey && publicKey !== '310f0d43-27c2-47a5-a76d-e55171d024f7') {
      const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
        headers: {
          'Authorization': `Bearer ${publicKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        results.assistantStatus = 'accessible';
        console.log('✅ [VapiDiagnostics] Assistant is accessible');
      } else if (response.status === 401) {
        results.issues.push('Unauthorized access to assistant - API key may be invalid');
        results.recommendations.push('Verify your Vapi API key has correct permissions');
        results.assistantStatus = 'unauthorized';
      } else if (response.status === 404) {
        results.issues.push('Assistant not found - may have been deleted or ID is incorrect');
        results.recommendations.push('Verify the assistant ID exists in your Vapi dashboard');
        results.assistantStatus = 'not_found';
      } else {
        results.issues.push(`Assistant check failed with status: ${response.status}`);
        results.assistantStatus = 'error';
      }
    } else {
      results.assistantStatus = 'skipped';
    }
  } catch (error) {
    results.issues.push(`Error checking assistant: ${error.message}`);
    results.assistantStatus = 'error';
  }

  // 3. Check Network Connectivity
  try {
    const response = await fetch('https://api.vapi.ai/health', {
      method: 'GET',
      timeout: 5000
    });

    if (response.ok) {
      results.networkStatus = 'connected';
      console.log('✅ [VapiDiagnostics] Network connectivity to Vapi is good');
    } else {
      results.issues.push('Vapi API is not responding properly');
      results.networkStatus = 'degraded';
    }
  } catch (error) {
    results.issues.push(`Network connectivity issue: ${error.message}`);
    results.recommendations.push('Check your internet connection and firewall settings');
    results.networkStatus = 'failed';
  }

  // 4. Check Audio/Microphone Permissions
  try {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop()); // Clean up
      results.audioStatus = 'available';
      console.log('✅ [VapiDiagnostics] Microphone access is available');
    } else {
      results.issues.push('Microphone access not available');
      results.recommendations.push('Enable microphone permissions in your browser');
      results.audioStatus = 'unavailable';
    }
  } catch (error) {
    results.issues.push(`Microphone access error: ${error.message}`);
    results.recommendations.push('Grant microphone permissions and ensure no other app is using it');
    results.audioStatus = 'denied';
  }

  // 5. Generate Summary
  const criticalIssues = results.issues.filter(issue =>
    issue.includes('API key') ||
    issue.includes('assistant ID') ||
    issue.includes('Unauthorized')
  );

  if (criticalIssues.length > 0) {
    results.recommendations.unshift('🚨 CRITICAL: Fix API key and assistant configuration first');
  }

  console.log('🔍 [VapiDiagnostics] Diagnosis complete:', results);
  return results;
};

export default {
  testVapiDirectConnection,
  testGetAssistant,
  getVapiEnvironmentConfig,
  diagnoseVapiEjectionIssue
};
