<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Test App Integration</h1>
        <p>This test verifies that the Vapi SDK fix works with the actual LegalScout Voice app components.</p>
        
        <div id="status" class="status info">
            Ready to test...
        </div>

        <button onclick="testAppIntegration()">
            🚀 Test App Integration
        </button>
        
        <button onclick="testVapiLoader()">
            📦 Test Vapi Loader
        </button>
        
        <button onclick="testVapiService()">
            🔧 Test Vapi Service
        </button>

        <div id="logs" class="log"></div>
    </div>

    <script type="module">
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // Test the app integration
        async function testAppIntegration() {
            updateStatus('Testing app integration...', 'info');
            document.getElementById('logs').innerHTML = '';
            
            log('🚀 Starting app integration test...');
            
            try {
                // Test 1: Import the Vapi loader from the app
                log('📋 Test 1: Importing Vapi loader from app');
                const { loadVapiSDK, createVapiInstance } = await import('/src/utils/vapiLoader.js');
                log('✅ Vapi loader imported successfully');
                
                // Test 2: Load the SDK
                log('📋 Test 2: Loading Vapi SDK');
                const VapiClass = await loadVapiSDK();
                
                if (typeof VapiClass === 'function') {
                    log('✅ Vapi SDK loaded successfully');
                    
                    // Test 3: Create instance using the loader
                    log('📋 Test 3: Creating Vapi instance');
                    const testApiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
                    const vapiInstance = await createVapiInstance(testApiKey);
                    
                    if (vapiInstance && typeof vapiInstance.start === 'function') {
                        log('✅ Vapi instance created with start method');
                        log('✅ App integration test passed!');
                        updateStatus('✅ App integration working correctly!', 'success');
                    } else {
                        log('❌ Vapi instance missing start method');
                        updateStatus('❌ Instance validation failed', 'error');
                    }
                    
                } else {
                    log(`❌ Vapi SDK not loaded correctly, got: ${typeof VapiClass}`);
                    updateStatus('❌ SDK loading failed', 'error');
                }
                
            } catch (error) {
                log(`❌ App integration test failed: ${error.message}`, 'error');
                updateStatus(`❌ Integration failed: ${error.message}`, 'error');
            }
        }

        // Test the Vapi loader directly
        async function testVapiLoader() {
            updateStatus('Testing Vapi loader...', 'info');
            document.getElementById('logs').innerHTML = '';
            
            log('📦 Testing Vapi loader directly...');
            
            try {
                const { loadVapiSDK, isVapiLoaded, getVapiClass } = await import('/src/utils/vapiLoader.js');
                
                log('✅ Vapi loader module imported');
                log(`Initial loaded state: ${isVapiLoaded()}`);
                
                const VapiClass = await loadVapiSDK();
                log(`After loading - loaded state: ${isVapiLoaded()}`);
                log(`Vapi class type: ${typeof VapiClass}`);
                
                const retrievedClass = getVapiClass();
                log(`Retrieved class matches: ${VapiClass === retrievedClass}`);
                
                updateStatus('✅ Vapi loader test completed', 'success');
                
            } catch (error) {
                log(`❌ Vapi loader test failed: ${error.message}`, 'error');
                updateStatus(`❌ Loader test failed: ${error.message}`, 'error');
            }
        }

        // Test if Vapi service can be imported
        async function testVapiService() {
            updateStatus('Testing Vapi service...', 'info');
            document.getElementById('logs').innerHTML = '';
            
            log('🔧 Testing Vapi service import...');
            
            try {
                // Try to import the Vapi service
                const vapiServiceModule = await import('/src/services/vapiService.jsx');
                log('✅ Vapi service imported successfully');
                log(`Service exports: ${Object.keys(vapiServiceModule).join(', ')}`);
                
                updateStatus('✅ Vapi service test completed', 'success');
                
            } catch (error) {
                log(`❌ Vapi service test failed: ${error.message}`, 'error');
                updateStatus(`❌ Service test failed: ${error.message}`, 'error');
            }
        }

        // Make functions available globally
        window.testAppIntegration = testAppIntegration;
        window.testVapiLoader = testVapiLoader;
        window.testVapiService = testVapiService;
    </script>
</body>
</html>
