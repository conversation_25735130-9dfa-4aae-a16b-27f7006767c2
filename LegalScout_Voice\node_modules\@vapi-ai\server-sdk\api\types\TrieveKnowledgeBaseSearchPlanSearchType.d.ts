/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the search method used when searching for relevant chunks from the vector store.
 */
export type TrieveKnowledgeBaseSearchPlanSearchType = "fulltext" | "semantic" | "hybrid" | "bm25";
export declare const TrieveKnowledgeBaseSearchPlanSearchType: {
    readonly Fulltext: "fulltext";
    readonly Semantic: "semantic";
    readonly Hybrid: "hybrid";
    readonly Bm25: "bm25";
};
