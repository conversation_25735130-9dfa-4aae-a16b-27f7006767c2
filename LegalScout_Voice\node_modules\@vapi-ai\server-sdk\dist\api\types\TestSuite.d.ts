/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface TestSuite {
    /** This is the unique identifier for the test suite. */
    id: string;
    /** This is the unique identifier for the org that this test suite belongs to. */
    orgId: string;
    /** This is the ISO 8601 date-time string of when the test suite was created. */
    createdAt: string;
    /** This is the ISO 8601 date-time string of when the test suite was last updated. */
    updatedAt: string;
    /** This is the name of the test suite. */
    name?: string;
    /** This is the phone number ID associated with this test suite. */
    phoneNumberId?: string;
    /**
     * Override the default tester plan by providing custom assistant configuration for the test agent.
     *
     * We recommend only using this if you are confident, as we have already set sensible defaults on the tester plan.
     */
    testerPlan?: Vapi.TesterPlan;
    /** These are the configuration for the assistant / phone number that is being tested. */
    targetPlan?: Vapi.TargetPlan;
}
