/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface UpdateByoSipTrunkCredentialDto {
    /** This is the name of credential. This is just for your reference. */
    name?: string;
    /** This is the list of SIP trunk's gateways. */
    gateways?: Vapi.SipTrunkGateway[];
    /** This can be used to configure the outbound authentication if required by the SIP trunk. */
    outboundAuthenticationPlan?: Vapi.SipTrunkOutboundAuthenticationPlan;
    /**
     * This ensures the outbound origination attempts have a leading plus. Defaults to false to match conventional telecom behavior.
     *
     * Usage:
     * - Vonage/Twilio requires leading plus for all outbound calls. Set this to true.
     *
     * @default false
     */
    outboundLeadingPlusEnabled?: boolean;
    /** This can be used to configure the tech prefix on outbound calls. This is an advanced property. */
    techPrefix?: string;
    /** This can be used to enable the SIP diversion header for authenticating the calling number if the SIP trunk supports it. This is an advanced property. */
    sipDiversionHeader?: string;
    /** This is an advanced configuration for enterprise deployments. This uses the onprem SBC to trunk into the SIP trunk's `gateways`, rather than the managed SBC provided by Vapi. */
    sbcConfiguration?: Vapi.SbcConfiguration;
}
