<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Real Vapi Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Real Vapi Flow (Like Your App)</h1>
        <p>This test replicates exactly how your LegalScout Voice app creates calls.</p>
        
        <div id="status" class="status info">
            Ready to test...
        </div>

        <button id="testFlow" onclick="testCompleteFlow()">
            🚀 Test Complete Flow
        </button>
        
        <button id="testMicrophone" onclick="testMicrophone()">
            🎤 Test Microphone
        </button>
        
        <button id="startCall" onclick="startTestCall()" disabled>
            📞 Start Test Call
        </button>
        
        <button id="stopCall" onclick="stopTestCall()" disabled>
            ⏹️ Stop Call
        </button>

        <div id="logs" class="log"></div>
    </div>

    <script type="module">
        // Import Vapi SDK using ES modules (more reliable)
        let Vapi;

        async function loadVapiSDK() {
            try {
                console.log('Attempting to load Vapi SDK using fixed URLs...');

                // Use the correct CDN sources (matching our fixed vapiLoader.js)
                const cdnSources = [
                    // Try the official Vapi HTML script tag (from official docs)
                    'https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js',
                    // Try unpkg with correct structure
                    'https://unpkg.com/@vapi-ai/web@2.3.5/dist/vapi.js',
                    'https://unpkg.com/@vapi-ai/web@2.3.1/dist/vapi.js',
                    'https://unpkg.com/@vapi-ai/web@latest/dist/vapi.js',
                    // Try jsdelivr with correct structure
                    'https://cdn.jsdelivr.net/npm/@vapi-ai/web@2.3.5/dist/vapi.js',
                    'https://cdn.jsdelivr.net/npm/@vapi-ai/web@2.3.1/dist/vapi.js',
                    'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/vapi.js'
                ];

                for (let i = 0; i < cdnSources.length; i++) {
                    const src = cdnSources[i];

                    try {
                        console.log(`Trying CDN source ${i + 1}/${cdnSources.length}: ${src}`);

                        await new Promise((resolve, reject) => {
                            // Don't load if already available
                            if (window.Vapi) {
                                resolve();
                                return;
                            }

                            const script = document.createElement('script');
                            script.src = src;
                            script.async = true;
                            script.crossOrigin = 'anonymous';

                            const timeout = setTimeout(() => {
                                script.remove();
                                reject(new Error('CDN loading timeout'));
                            }, 10000);

                            script.onload = () => {
                                clearTimeout(timeout);
                                console.log(`✅ Successfully loaded from CDN: ${src}`);

                                // Wait a bit for the script to initialize
                                setTimeout(() => {
                                    if (window.Vapi && typeof window.Vapi === 'function') {
                                        Vapi = window.Vapi;
                                        resolve();
                                    } else {
                                        reject(new Error('Vapi not available after script load'));
                                    }
                                }, 500);
                            };

                            script.onerror = () => {
                                clearTimeout(timeout);
                                script.remove();
                                reject(new Error(`Failed to load from CDN: ${src}`));
                            };

                            document.head.appendChild(script);
                        });

                        // If we get here, the script loaded successfully
                        if (window.Vapi && typeof window.Vapi === 'function') {
                            console.log(`✅ Vapi SDK loaded successfully from: ${src}`);
                            return true;
                        }

                    } catch (error) {
                        console.log(`❌ CDN source failed: ${src} - ${error.message}`);

                        // Add delay before trying next source
                        if (i < cdnSources.length - 1) {
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                    }
                }

                throw new Error('Failed to load Vapi SDK from all CDN sources');
            } catch (error) {
                console.error('All Vapi loading methods failed:', error);
                return false;
            }
        }

        // Make functions available globally
        window.loadVapiSDK = loadVapiSDK;
        window.getVapi = () => Vapi;
    </script>

    <script>
        // Configuration matching your app
        const API_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
        const ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
        
        let vapi = null;
        let isCallActive = false;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function updateButtons() {
            document.getElementById('startCall').disabled = !vapi || isCallActive;
            document.getElementById('stopCall').disabled = !isCallActive;
        }
        
        async function testMicrophone() {
            updateStatus('Testing microphone access...', 'info');
            log('🎤 Testing microphone access...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                log('✅ Microphone access granted', 'success');
                updateStatus('Microphone test passed', 'success');
                return true;
            } catch (error) {
                log(`❌ Microphone access denied: ${error.message}`, 'error');
                updateStatus('Microphone test failed - grant permissions', 'error');
                return false;
            }
        }
        
        async function initializeVapi() {
            updateStatus('Initializing Vapi SDK...', 'info');
            log('🔧 Initializing Vapi SDK...');

            try {
                // Load Vapi SDK using our enhanced loader
                log('⏳ Loading Vapi SDK...');
                const loaded = await window.loadVapiSDK();

                if (!loaded) {
                    throw new Error('Failed to load Vapi SDK from all sources');
                }

                const VapiClass = window.getVapi();
                if (!VapiClass) {
                    throw new Error('Vapi class not available after loading');
                }

                log(`✅ Vapi SDK loaded successfully, creating instance with key: ${API_KEY.substring(0, 8)}...`);
                
                // Create Vapi instance (exactly like your app)
                vapi = new VapiClass(API_KEY);
                
                // Set up event listeners (exactly like your app)
                vapi.on('call-start', () => {
                    log('📞 Call started!', 'success');
                    isCallActive = true;
                    updateButtons();
                    updateStatus('Call active', 'success');
                });
                
                vapi.on('call-end', () => {
                    log('📞 Call ended', 'info');
                    isCallActive = false;
                    updateButtons();
                    updateStatus('Call ended', 'info');
                });
                
                vapi.on('error', (error) => {
                    log(`❌ Vapi error: ${error.message}`, 'error');
                    updateStatus(`Error: ${error.message}`, 'error');
                });
                
                vapi.on('message', (message) => {
                    if (message.type === 'transcript') {
                        log(`💬 ${message.role}: ${message.transcript}`);
                    }
                });
                
                log('✅ Vapi instance created and event listeners set up', 'success');
                updateStatus('Vapi initialized successfully', 'success');
                updateButtons();
                return true;
                
            } catch (error) {
                log(`❌ Failed to initialize Vapi: ${error.message}`, 'error');
                updateStatus(`Initialization failed: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function startTestCall() {
            if (!vapi) {
                log('❌ Vapi not initialized', 'error');
                return;
            }
            
            updateStatus('Starting call...', 'info');
            log(`📞 Starting call with assistant: ${ASSISTANT_ID}`);
            
            try {
                // This is exactly how your app starts calls
                await vapi.start(ASSISTANT_ID);
                log('✅ Call start request sent', 'success');
                
            } catch (error) {
                log(`❌ Failed to start call: ${error.message}`, 'error');
                updateStatus(`Call failed: ${error.message}`, 'error');
            }
        }
        
        async function stopTestCall() {
            if (!vapi || !isCallActive) {
                log('❌ No active call to stop', 'error');
                return;
            }
            
            updateStatus('Stopping call...', 'info');
            log('⏹️ Stopping call...');
            
            try {
                await vapi.stop();
                log('✅ Call stopped', 'success');
                
            } catch (error) {
                log(`❌ Failed to stop call: ${error.message}`, 'error');
                updateStatus(`Stop failed: ${error.message}`, 'error');
            }
        }
        
        async function testCompleteFlow() {
            updateStatus('Running complete flow test...', 'info');
            document.getElementById('logs').innerHTML = '';
            
            log('🚀 Starting complete Vapi flow test...');
            log('This replicates exactly how your LegalScout Voice app works');
            
            // Step 1: Test microphone
            log('\n📋 Step 1: Testing microphone access');
            const microphoneOk = await testMicrophone();
            
            if (!microphoneOk) {
                log('❌ Cannot proceed without microphone access', 'error');
                updateStatus('Test failed - microphone required', 'error');
                return;
            }
            
            // Step 2: Initialize Vapi
            log('\n📋 Step 2: Initializing Vapi SDK');
            const vapiOk = await initializeVapi();
            
            if (!vapiOk) {
                log('❌ Cannot proceed without Vapi initialization', 'error');
                updateStatus('Test failed - Vapi initialization failed', 'error');
                return;
            }
            
            log('\n✅ Complete flow test passed!', 'success');
            log('💡 You can now test actual calls with the buttons above');
            updateStatus('Flow test completed - ready for calls', 'success');
        }
        
        // Auto-run flow test when page loads
        window.addEventListener('load', () => {
            setTimeout(testCompleteFlow, 1000);
        });
    </script>
</body>
</html>
