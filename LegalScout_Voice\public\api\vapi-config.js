// Simple API endpoint to provide Vapi configuration
// This simulates a backend API call for the diagnosis tool

// In a real application, this would be handled by your backend
// For testing purposes, we'll use the environment variables

const vapiConfig = {
    apiKey: '6734febc-fc65-4669-93b0-929b31ff6564', // The working API key we identified
    baseUrl: 'https://api.vapi.ai',
    assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a', // Test assistant ID
    environment: 'development'
};

// Export for use in diagnosis
if (typeof module !== 'undefined' && module.exports) {
    module.exports = vapiConfig;
} else if (typeof window !== 'undefined') {
    window.vapiConfig = vapiConfig;
}
