<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Official Vapi Method</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Official Vapi Method</h1>
        <p>This test uses the exact method from the official Vapi documentation.</p>
        
        <div id="status" class="status info">
            Ready to test...
        </div>

        <button onclick="testOfficialMethod()">
            🚀 Test Official Method
        </button>
        
        <button onclick="testMicrophone()">
            🎤 Test Microphone
        </button>
        
        <button id="startCall" onclick="startCall()" disabled>
            📞 Start Call
        </button>
        
        <button id="stopCall" onclick="stopCall()" disabled>
            ⏹️ Stop Call
        </button>

        <div id="logs" class="log"></div>
    </div>

    <!-- Official Vapi HTML Script Tag Method (from docs) -->
    <script>
        var vapiInstance = null;
        const assistant = "f9b97d13-f9c4-40af-a660-62ba5925ff2a"; // Your assistant ID
        const apiKey = "6734febc-fc65-4669-93b0-929b31ff6564"; // Your Public key
        const buttonConfig = {}; // Modify this as required

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function updateButtons() {
            document.getElementById('startCall').disabled = !vapiInstance;
            document.getElementById('stopCall').disabled = !vapiInstance;
        }

        async function testMicrophone() {
            updateStatus('Testing microphone access...', 'info');
            log('🎤 Testing microphone access...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                log('✅ Microphone access granted', 'success');
                updateStatus('Microphone test passed', 'success');
                return true;
            } catch (error) {
                log(`❌ Microphone access denied: ${error.message}`, 'error');
                updateStatus('Microphone test failed - grant permissions', 'error');
                return false;
            }
        }

        function testOfficialMethod() {
            updateStatus('Testing official Vapi method...', 'info');
            document.getElementById('logs').innerHTML = '';
            
            log('🚀 Starting official Vapi method test...');
            log('This uses the EXACT method from Vapi documentation');
            
            // Step 1: Test microphone
            log('\n📋 Step 1: Testing microphone access');
            testMicrophone().then(microphoneOk => {
                if (!microphoneOk) {
                    log('❌ Cannot proceed without microphone access', 'error');
                    updateStatus('Test failed - microphone required', 'error');
                    return;
                }
                
                // Step 2: Load official Vapi script
                log('\n📋 Step 2: Loading official Vapi script');
                loadOfficialVapiScript();
            });
        }

        function loadOfficialVapiScript() {
            log('📦 Loading official Vapi script...');
            
            (function (d, t) {
                var g = document.createElement(t),
                    s = d.getElementsByTagName(t)[0];
                g.src = "https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js";
                g.defer = true;
                g.async = true;
                s.parentNode.insertBefore(g, s);

                g.onload = function () {
                    log('✅ Official Vapi script loaded successfully', 'success');
                    
                    if (window.vapiSDK) {
                        log('✅ window.vapiSDK is available', 'success');
                        
                        try {
                            vapiInstance = window.vapiSDK.run({
                                apiKey: apiKey, // mandatory
                                assistant: assistant, // mandatory
                                config: buttonConfig, // optional
                            });
                            
                            log('✅ Vapi instance created successfully', 'success');
                            log('✅ Official method test completed!', 'success');
                            updateStatus('Official method working correctly!', 'success');
                            updateButtons();
                            
                        } catch (error) {
                            log(`❌ Failed to create Vapi instance: ${error.message}`, 'error');
                            updateStatus(`Instance creation failed: ${error.message}`, 'error');
                        }
                        
                    } else {
                        log('❌ window.vapiSDK not available after script load', 'error');
                        updateStatus('vapiSDK not available', 'error');
                    }
                };

                g.onerror = function () {
                    log('❌ Failed to load official Vapi script', 'error');
                    updateStatus('Script loading failed', 'error');
                };
            })(document, "script");
        }

        function startCall() {
            if (!vapiInstance) {
                log('❌ Vapi instance not available', 'error');
                return;
            }
            
            log('📞 Starting call...');
            updateStatus('Starting call...', 'info');
            
            try {
                // The official method might have different API
                if (typeof vapiInstance.start === 'function') {
                    vapiInstance.start();
                    log('✅ Call started using vapiInstance.start()', 'success');
                } else if (typeof vapiInstance.call === 'function') {
                    vapiInstance.call();
                    log('✅ Call started using vapiInstance.call()', 'success');
                } else {
                    log(`❌ No start/call method found. Available methods: ${Object.keys(vapiInstance).join(', ')}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Failed to start call: ${error.message}`, 'error');
                updateStatus(`Call failed: ${error.message}`, 'error');
            }
        }

        function stopCall() {
            if (!vapiInstance) {
                log('❌ Vapi instance not available', 'error');
                return;
            }
            
            log('⏹️ Stopping call...');
            updateStatus('Stopping call...', 'info');
            
            try {
                if (typeof vapiInstance.stop === 'function') {
                    vapiInstance.stop();
                    log('✅ Call stopped', 'success');
                } else {
                    log(`❌ No stop method found. Available methods: ${Object.keys(vapiInstance).join(', ')}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Failed to stop call: ${error.message}`, 'error');
                updateStatus(`Stop failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
