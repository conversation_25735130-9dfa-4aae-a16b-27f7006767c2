/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface VapiCost {
    /** This is the type of cost, always 'vapi' for this class. */
    type: "vapi";
    /** This is the sub type of the cost. */
    subType: Vapi.VapiCostSubType;
    /** This is the minutes of Vapi usage. This should match `call.endedAt` - `call.startedAt`. */
    minutes: number;
    /** This is the cost of the component in USD. */
    cost: number;
}
